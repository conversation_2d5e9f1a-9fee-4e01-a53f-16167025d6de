[tool.ruff]
lint.select = [
    "A", "B", "B<PERSON>", "C4", "DJ", "DT<PERSON>", "E", "ERA", "F", "FLY",
    "G", "I", "ICN", "INP", "ISC", "N", "PERF", "PGH", "PIE",
    "PL", "PLE", "PLR", "PLW", "PT", "PTH", "RET", "RSE",
    "RUF", "S", "SIM", "SLF", "T10", "T20", "TRY", "UP", "W"
]
lint.ignore = ["N806", "PERF401", "RUF012", "SIM105", "TRY003", "PLR2004"]
lint.per-file-ignores = { "*/test_*.py" = ["S101"] }
line-length = 88
extend-exclude = ["*/migrations/*.py", "manage.py"]

[tool.ruff.format]
exclude = ["migrations"]

[tool.ruff.lint.isort]
known-first-party = ["project", "hatterparty"]
known-third-party = [
    "IPython",
    "PIL",
    "authlib",
    "corsheaders",
    "django",
    "django_extensions",
    "django_filters",
    "drf_excel",
    "google-api-python-client",
    "google-auth-oauthlib",
    "httpx",
    "jsonschema",
    "lxml",
    "magic",
    "openpyxl",
    "pdfplumber",
    "pyodbc",
    "python_calamine",
    "rest_framework",
    "sentry_sdk",
    "sqlparse",
    "sshtunnel",
    "storages",
    "stripe",
    "trycourier",
    "wand",
    "weasyprint",
    "xlsxwriter",
]

[tool.ruff.lint.pylint]
max-args = 10
max-branches = 15

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "project.settings"
