from django.contrib.auth.hashers import check_password, make_password
from django.db import models
from django.utils import timezone


class BaseModel(models.Model):
    created_at = models.DateTimeField(verbose_name="Data creazione", auto_now_add=True)
    modified_at = models.DateTimeField(
        verbose_name="Data ultima modifica", auto_now=True
    )

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        # modified_at is not updated when save is called with update_fields
        if self.pk and "update_fields" in kwargs:
            kwargs["update_fields"].append("modified_at")
        super().save(*args, **kwargs)


class BaseUserModel(BaseModel):
    is_authenticated = True

    email = models.EmailField(unique=True)
    password_hash = models.CharField(max_length=255, blank=True)
    last_login = models.DateTimeField(
        verbose_name="Ultimo accesso", null=True, blank=True
    )
    is_active = models.BooleanField(verbose_name="Attivo", default=True)

    class Meta:
        abstract = True

    def __str__(self):
        return self.email

    @classmethod
    def authenticate(cls, email, password):
        try:
            user = cls.objects.get(email__iexact=email)
        except cls.DoesNotExist:
            return None
        if user.is_active and user.check_password(password):
            user.last_login = timezone.localtime()
            user.save(update_fields=["last_login"])
            return user
        return None

    def set_password(self, password) -> None:
        self.password_hash = make_password(password)

    def check_password(self, password: str) -> bool:
        return check_password(
            password,
            self.password_hash,
            lambda pwd: self.set_password(pwd),
        )


class TemporalValidityMixin(models.Model):
    """
    Mixin to add temporal validity fields to models.
    Allows setting a validity period for records.
    """
    valid_from = models.DateTimeField(
        verbose_name="Valid from",
        null=True,
        blank=True,
        help_text="Start date/time when this record becomes valid"
    )
    valid_until = models.DateTimeField(
        verbose_name="Valid until",
        null=True,
        blank=True,
        help_text="End date/time when this record expires"
    )

    class Meta:
        abstract = True

    def is_valid_at(self, when=None):
        """
        Check if the record is valid at a specific datetime.
        If when is None, uses current datetime.
        """
        if when is None:
            when = timezone.now()

        if self.valid_from and when < self.valid_from:
            return False
        if self.valid_until and when > self.valid_until:
            return False
        return True

    @property
    def is_currently_valid(self):
        """Check if the record is currently valid."""
        return self.is_valid_at()


class ActivationMixin(models.Model):
    """
    Mixin to add activation functionality to models.
    Supports both boolean and datetime-based activation.
    """
    is_active = models.BooleanField(
        verbose_name="Active",
        default=True,
        help_text="Whether this record is active"
    )
    activated_at = models.DateTimeField(
        verbose_name="Activated at",
        null=True,
        blank=True,
        help_text="When this record was activated"
    )
    deactivated_at = models.DateTimeField(
        verbose_name="Deactivated at",
        null=True,
        blank=True,
        help_text="When this record was deactivated"
    )

    class Meta:
        abstract = True

    def activate(self):
        """Activate the record."""
        self.is_active = True
        self.activated_at = timezone.now()
        self.deactivated_at = None

    def deactivate(self):
        """Deactivate the record."""
        self.is_active = False
        self.deactivated_at = timezone.now()

    @property
    def activation_status(self):
        """Get human-readable activation status."""
        if self.is_active:
            return "Active"
        return "Inactive"
