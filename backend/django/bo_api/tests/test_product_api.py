from decimal import Decimal
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status

from hatterparty.models import BOUser, Category, Product


class ProductAPITestCase(TestCase):
    """Base test case for Product API tests."""

    def setUp(self):
        self.client = APIClient()
        self.user = BOUser.objects.create(
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            is_active=True
        )
        self.user.set_password('testpass123')
        self.user.save()
        
        # Force authentication for tests
        self.client.force_authenticate(user=self.user)


class ProductAPITest(ProductAPITestCase):
    """Test cases for Product API endpoints."""

    def setUp(self):
        super().setUp()
        self.category = Category.objects.create(
            name='Test Category',
            slug='test-category'
        )
        self.product_data = {
            'name': 'Test Product',
            'description': 'Test product description',
            'short_description': 'Short description',
            'price': '99.99',
            'quantity_available': 10,
            'category': self.category.pk,
            'sku': 'TEST-001',
            'weight': '5.50',
            'dimensions': '100x50x30 cm',
            'is_active': True,
        }

    def test_create_product(self):
        """Test creating a product via API."""
        url = reverse('products-list')
        response = self.client.post(url, self.product_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Test Product')
        self.assertEqual(response.data['price'], '99.99')

    def test_list_products(self):
        """Test listing products via API."""
        Product.objects.create(
            name='Test Product',
            price=Decimal('99.99'),
            quantity_available=10,
            category=self.category
        )
        
        url = reverse('products-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

    def test_activate_product(self):
        """Test activating a product via API."""
        product = Product.objects.create(
            name='Test Product',
            price=Decimal('99.99'),
            quantity_available=10,
            category=self.category,
            is_active=False
        )
        
        url = reverse('products-activate', kwargs={'pk': product.pk})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'activated')
        
        product.refresh_from_db()
        self.assertTrue(product.is_active)

    def test_deactivate_product(self):
        """Test deactivating a product via API."""
        product = Product.objects.create(
            name='Test Product',
            price=Decimal('99.99'),
            quantity_available=10,
            category=self.category,
            is_active=True
        )
        
        url = reverse('products-deactivate', kwargs={'pk': product.pk})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'deactivated')
        
        product.refresh_from_db()
        self.assertFalse(product.is_active)

    def test_bulk_activate_products(self):
        """Test bulk activating products via API."""
        product1 = Product.objects.create(
            name='Product 1',
            price=Decimal('99.99'),
            quantity_available=10,
            category=self.category,
            is_active=False
        )
        product2 = Product.objects.create(
            name='Product 2',
            price=Decimal('149.99'),
            quantity_available=5,
            category=self.category,
            is_active=False
        )
        
        url = reverse('products-bulk-activate')
        data = {'product_ids': [product1.pk, product2.pk]}
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        
        product1.refresh_from_db()
        product2.refresh_from_db()
        self.assertTrue(product1.is_active)
        self.assertTrue(product2.is_active)

    def test_filter_products_by_category(self):
        """Test filtering products by category via API."""
        other_category = Category.objects.create(
            name='Other Category',
            slug='other-category'
        )
        
        Product.objects.create(
            name='Product 1',
            price=Decimal('99.99'),
            quantity_available=10,
            category=self.category
        )
        Product.objects.create(
            name='Product 2',
            price=Decimal('149.99'),
            quantity_available=5,
            category=other_category
        )
        
        url = reverse('products-list')
        response = self.client.get(url, {'category': self.category.pk})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Product 1')

    def test_search_products(self):
        """Test searching products via API."""
        Product.objects.create(
            name='Red Chair',
            description='A beautiful red chair',
            price=Decimal('99.99'),
            quantity_available=10,
            category=self.category
        )
        Product.objects.create(
            name='Blue Table',
            description='A sturdy blue table',
            price=Decimal('149.99'),
            quantity_available=5,
            category=self.category
        )
        
        url = reverse('products-list')
        response = self.client.get(url, {'search': 'chair'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Red Chair')
