from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status

from hatterparty.models import BOUser, Category


class CategoryAPITestCase(TestCase):
    """Base test case for Category API tests."""

    def setUp(self):
        self.client = APIClient()
        self.user = BOUser.objects.create(
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            is_active=True
        )
        self.user.set_password('testpass123')
        self.user.save()
        
        # Force authentication for tests
        self.client.force_authenticate(user=self.user)

    def authenticate(self):
        """Helper method to authenticate the test client."""
        self.client.force_authenticate(user=self.user)


class CategoryAPITest(CategoryAPITestCase):
    """Test cases for Category API endpoints."""

    def setUp(self):
        super().setUp()
        self.category_data = {
            'name': 'Test Category',
            'description': 'Test category description',
            'slug': 'test-category',
        }

    def test_create_category(self):
        """Test creating a category via API."""
        self.authenticate()
        url = reverse('categories-list')
        response = self.client.post(url, self.category_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Test Category')
        self.assertEqual(response.data['slug'], 'test-category')

    def test_list_categories(self):
        """Test listing categories via API."""
        self.authenticate()
        Category.objects.create(**self.category_data)
        
        url = reverse('categories-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

    def test_retrieve_category(self):
        """Test retrieving a specific category via API."""
        self.authenticate()
        category = Category.objects.create(**self.category_data)
        
        url = reverse('categories-detail', kwargs={'pk': category.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Test Category')

    def test_update_category(self):
        """Test updating a category via API."""
        self.authenticate()
        category = Category.objects.create(**self.category_data)
        
        updated_data = {
            'name': 'Updated Category',
            'description': 'Updated description',
            'slug': 'updated-category',
        }
        
        url = reverse('categories-detail', kwargs={'pk': category.pk})
        response = self.client.put(url, updated_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Updated Category')

    def test_delete_category(self):
        """Test deleting a category via API."""
        self.authenticate()
        category = Category.objects.create(**self.category_data)
        
        url = reverse('categories-detail', kwargs={'pk': category.pk})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(Category.objects.filter(pk=category.pk).exists())
