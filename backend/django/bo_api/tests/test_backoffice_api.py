from decimal import Decimal
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status

from hatterparty.models import BOUser, Category, Product, Service, Location


class BackofficeAPITestCase(TestCase):
    """Base test case for backoffice API tests."""

    def setUp(self):
        self.client = APIClient()
        self.user = BOUser.objects.create(
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            is_active=True
        )
        self.user.set_password('testpass123')
        self.user.save()
        
        # Force authentication for tests
        self.client._force_auth_user = self.user

    def authenticate(self):
        """Helper method to authenticate the test client."""
        self.client.force_authenticate(user=self.user)


class CategoryAPITest(BackofficeAPITestCase):
    """Test cases for Category API endpoints."""

    def setUp(self):
        super().setUp()
        self.category_data = {
            'name': 'Test Category',
            'description': 'Test category description',
            'slug': 'test-category',
        }

    def test_create_category(self):
        """Test creating a category via API."""
        self.authenticate()
        url = reverse('categories-list')
        response = self.client.post(url, self.category_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Test Category')
        self.assertEqual(response.data['slug'], 'test-category')

    def test_list_categories(self):
        """Test listing categories via API."""
        self.authenticate()
        Category.objects.create(**self.category_data)
        
        url = reverse('categories-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

    def test_retrieve_category(self):
        """Test retrieving a specific category via API."""
        self.authenticate()
        category = Category.objects.create(**self.category_data)
        
        url = reverse('categories-detail', kwargs={'pk': category.pk})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Test Category')

    def test_update_category(self):
        """Test updating a category via API."""
        self.authenticate()
        category = Category.objects.create(**self.category_data)
        
        updated_data = {
            'name': 'Updated Category',
            'description': 'Updated description',
            'slug': 'updated-category',
        }
        
        url = reverse('categories-detail', kwargs={'pk': category.pk})
        response = self.client.put(url, updated_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Updated Category')

    def test_delete_category(self):
        """Test deleting a category via API."""
        self.authenticate()
        category = Category.objects.create(**self.category_data)
        
        url = reverse('categories-detail', kwargs={'pk': category.pk})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(Category.objects.filter(pk=category.pk).exists())


class ProductAPITest(BackofficeAPITestCase):
    """Test cases for Product API endpoints."""

    def setUp(self):
        super().setUp()
        self.category = Category.objects.create(
            name='Test Category',
            slug='test-category'
        )
        self.product_data = {
            'name': 'Test Product',
            'description': 'Test product description',
            'short_description': 'Short description',
            'price': '99.99',
            'quantity_available': 10,
            'category': self.category.pk,
            'sku': 'TEST-001',
            'weight': '5.50',
            'dimensions': '100x50x30 cm',
            'is_active': True,
        }

    def test_create_product(self):
        """Test creating a product via API."""
        self.authenticate()
        url = reverse('products-list')
        response = self.client.post(url, self.product_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Test Product')
        self.assertEqual(response.data['price'], '99.99')

    def test_list_products(self):
        """Test listing products via API."""
        self.authenticate()
        Product.objects.create(
            name='Test Product',
            price=Decimal('99.99'),
            quantity_available=10,
            category=self.category
        )
        
        url = reverse('products-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

    def test_activate_product(self):
        """Test activating a product via API."""
        self.authenticate()
        product = Product.objects.create(
            name='Test Product',
            price=Decimal('99.99'),
            quantity_available=10,
            category=self.category,
            is_active=False
        )
        
        url = reverse('products-activate', kwargs={'pk': product.pk})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'activated')
        
        product.refresh_from_db()
        self.assertTrue(product.is_active)

    def test_deactivate_product(self):
        """Test deactivating a product via API."""
        self.authenticate()
        product = Product.objects.create(
            name='Test Product',
            price=Decimal('99.99'),
            quantity_available=10,
            category=self.category,
            is_active=True
        )
        
        url = reverse('products-deactivate', kwargs={'pk': product.pk})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'deactivated')
        
        product.refresh_from_db()
        self.assertFalse(product.is_active)

    def test_bulk_activate_products(self):
        """Test bulk activating products via API."""
        self.authenticate()
        product1 = Product.objects.create(
            name='Product 1',
            price=Decimal('99.99'),
            quantity_available=10,
            category=self.category,
            is_active=False
        )
        product2 = Product.objects.create(
            name='Product 2',
            price=Decimal('149.99'),
            quantity_available=5,
            category=self.category,
            is_active=False
        )
        
        url = reverse('products-bulk-activate')
        data = {'product_ids': [product1.pk, product2.pk]}
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        
        product1.refresh_from_db()
        product2.refresh_from_db()
        self.assertTrue(product1.is_active)
        self.assertTrue(product2.is_active)

    def test_filter_products_by_category(self):
        """Test filtering products by category via API."""
        self.authenticate()
        other_category = Category.objects.create(
            name='Other Category',
            slug='other-category'
        )
        
        Product.objects.create(
            name='Product 1',
            price=Decimal('99.99'),
            quantity_available=10,
            category=self.category
        )
        Product.objects.create(
            name='Product 2',
            price=Decimal('149.99'),
            quantity_available=5,
            category=other_category
        )
        
        url = reverse('products-list')
        response = self.client.get(url, {'category': self.category.pk})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Product 1')

    def test_search_products(self):
        """Test searching products via API."""
        self.authenticate()
        Product.objects.create(
            name='Red Chair',
            description='A beautiful red chair',
            price=Decimal('99.99'),
            quantity_available=10,
            category=self.category
        )
        Product.objects.create(
            name='Blue Table',
            description='A sturdy blue table',
            price=Decimal('149.99'),
            quantity_available=5,
            category=self.category
        )
        
        url = reverse('products-list')
        response = self.client.get(url, {'search': 'chair'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Red Chair')


class ServiceAPITest(BackofficeAPITestCase):
    """Test cases for Service API endpoints."""

    def setUp(self):
        super().setUp()
        self.category = Category.objects.create(
            name='Service Category',
            slug='service-category'
        )
        self.service_data = {
            'name': 'Test Service',
            'description': 'Test service description',
            'short_description': 'Short service description',
            'price': '150.00',
            'duration_hours': 4,
            'category': self.category.pk,
            'max_participants': 20,
            'requires_equipment': True,
            'is_active': True,
        }

    def test_create_service(self):
        """Test creating a service via API."""
        self.authenticate()
        url = reverse('services-list')
        response = self.client.post(url, self.service_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Test Service')
        self.assertEqual(response.data['duration_hours'], 4)

    def test_list_services(self):
        """Test listing services via API."""
        self.authenticate()
        Service.objects.create(
            name='Test Service',
            price=Decimal('150.00'),
            category=self.category
        )
        
        url = reverse('services-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)


class LocationAPITest(BackofficeAPITestCase):
    """Test cases for Location API endpoints."""

    def setUp(self):
        super().setUp()
        self.category = Category.objects.create(
            name='Location Category',
            slug='location-category'
        )
        self.location_data = {
            'name': 'Test Location',
            'description': 'Test location description',
            'short_description': 'Short location description',
            'price': '500.00',
            'address': '123 Test Street',
            'city': 'Test City',
            'postal_code': '12345',
            'capacity': 100,
            'category': self.category.pk,
            'has_parking': True,
            'has_catering': False,
            'indoor': True,
            'outdoor': False,
            'is_active': True,
        }

    def test_create_location(self):
        """Test creating a location via API."""
        self.authenticate()
        url = reverse('locations-list')
        response = self.client.post(url, self.location_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Test Location')
        self.assertEqual(response.data['capacity'], 100)

    def test_list_locations(self):
        """Test listing locations via API."""
        self.authenticate()
        Location.objects.create(
            name='Test Location',
            price=Decimal('500.00'),
            address='123 Test Street',
            city='Test City',
            postal_code='12345',
            capacity=100,
            category=self.category
        )
        
        url = reverse('locations-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

    def test_get_cities(self):
        """Test getting list of cities via API."""
        self.authenticate()
        Location.objects.create(
            name='Location 1',
            price=Decimal('500.00'),
            address='123 Test Street',
            city='City A',
            postal_code='12345',
            capacity=100,
            category=self.category
        )
        Location.objects.create(
            name='Location 2',
            price=Decimal('600.00'),
            address='456 Test Avenue',
            city='City B',
            postal_code='67890',
            capacity=150,
            category=self.category
        )
        
        url = reverse('locations-cities')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('cities', response.data)
        self.assertEqual(len(response.data['cities']), 2)
        self.assertIn('City A', response.data['cities'])
        self.assertIn('City B', response.data['cities'])
