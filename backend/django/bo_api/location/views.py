from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response

from common.drf import BackofficeViewSetMixin
from hatterparty.models import Location
from .serializers import LocationSerializer, LocationListSerializer


class LocationViewSet(BackofficeViewSetMixin, viewsets.ModelViewSet):
    """
    ViewSet for managing locations.
    Provides CRUD operations for locations with filtering and search.
    """
    queryset = Location.objects.select_related('category').all()
    serializer_class = LocationSerializer
    filterset_fields = [
        'category',
        'is_active',
        'city',
        'has_parking',
        'has_catering',
        'indoor',
        'outdoor',
    ]
    search_fields = ['name', 'description', 'short_description', 'address', 'city']
    ordering_fields = ['name', 'price', 'capacity', 'city', 'created_at']
    ordering = ['name']

    def get_serializer_class(self):
        """Use simplified serializer for list actions."""
        if self.action == 'list':
            return LocationListSerializer
        return LocationSerializer

    def get_queryset(self):
        """Filter queryset based on query parameters."""
        queryset = super().get_queryset()
        
        # Filter by availability status
        available = self.request.query_params.get('available')
        if available is not None:
            if available.lower() in ['true', '1']:
                queryset = queryset.filter(is_active=True)
            elif available.lower() in ['false', '0']:
                queryset = queryset.filter(is_active=False)
        
        # Filter by price range
        min_price = self.request.query_params.get('min_price')
        max_price = self.request.query_params.get('max_price')
        if min_price:
            queryset = queryset.filter(price__gte=min_price)
        if max_price:
            queryset = queryset.filter(price__lte=max_price)
        
        # Filter by capacity range
        min_capacity = self.request.query_params.get('min_capacity')
        max_capacity = self.request.query_params.get('max_capacity')
        if min_capacity:
            queryset = queryset.filter(capacity__gte=min_capacity)
        if max_capacity:
            queryset = queryset.filter(capacity__lte=max_capacity)
        
        return queryset

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate a location."""
        location = self.get_object()
        location.activate()
        location.save()
        return Response({
            'status': 'activated',
            'message': f'Location "{location.name}" has been activated.'
        })

    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """Deactivate a location."""
        location = self.get_object()
        location.deactivate()
        location.save()
        return Response({
            'status': 'deactivated',
            'message': f'Location "{location.name}" has been deactivated.'
        })

    @action(detail=False, methods=['post'])
    def bulk_activate(self, request):
        """Bulk activate locations."""
        location_ids = request.data.get('location_ids', [])
        if not location_ids:
            return Response(
                {'error': 'No location IDs provided.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        locations = Location.objects.filter(id__in=location_ids)
        count = 0
        for location in locations:
            location.activate()
            location.save()
            count += 1
        
        return Response({
            'status': 'success',
            'message': f'Activated {count} locations.'
        })

    @action(detail=False, methods=['post'])
    def bulk_deactivate(self, request):
        """Bulk deactivate locations."""
        location_ids = request.data.get('location_ids', [])
        if not location_ids:
            return Response(
                {'error': 'No location IDs provided.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        locations = Location.objects.filter(id__in=location_ids)
        count = 0
        for location in locations:
            location.deactivate()
            location.save()
            count += 1
        
        return Response({
            'status': 'success',
            'message': f'Deactivated {count} locations.'
        })

    @action(detail=False, methods=['get'])
    def cities(self, request):
        """Get list of unique cities."""
        cities = Location.objects.values_list('city', flat=True).distinct().order_by('city')
        return Response({'cities': list(cities)})
