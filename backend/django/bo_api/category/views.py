from rest_framework import viewsets

from common.drf import BackofficeViewSetMixin
from hatterparty.models import Category
from .serializers import CategorySerializer, CategoryListSerializer


class CategoryViewSet(BackofficeViewSetMixin, viewsets.ModelViewSet):
    """
    ViewSet for managing categories.
    Provides CRUD operations for categories.
    """
    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    filterset_fields = ['name']
    search_fields = ['name', 'description', 'slug']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    def get_serializer_class(self):
        """Use simplified serializer for list actions."""
        if self.action == 'list':
            return CategoryListSerializer
        return CategorySerializer
