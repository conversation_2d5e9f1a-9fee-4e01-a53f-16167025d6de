from rest_framework import serializers

from hatterparty.models import Category


class CategorySerializer(serializers.ModelSerializer):
    """
    Serializer for Category model.
    """
    product_count = serializers.SerializerMethodField()
    service_count = serializers.SerializerMethodField()
    location_count = serializers.SerializerMethodField()

    class Meta:
        model = Category
        fields = [
            'id',
            'name',
            'description',
            'slug',
            'product_count',
            'service_count',
            'location_count',
            'created_at',
            'modified_at',
        ]
        read_only_fields = ['id', 'created_at', 'modified_at', 'product_count', 'service_count', 'location_count']

    def get_product_count(self, obj):
        """Get the number of products in this category."""
        return obj.products.count()

    def get_service_count(self, obj):
        """Get the number of services in this category."""
        return obj.services.count()

    def get_location_count(self, obj):
        """Get the number of locations in this category."""
        return obj.locations.count()


class CategoryListSerializer(serializers.ModelSerializer):
    """Simplified Category serializer for list views."""
    
    class Meta:
        model = Category
        fields = ['id', 'name', 'slug']
