from django.urls import include, path
from rest_framework import routers

from .auth.views import AuthViewSet
from .bo_user.views import BOUserViewSet
from .category.views import CategoryViewSet
from .product.views import ProductViewSet
from .service.views import ServiceViewSet
from .location.views import LocationViewSet

router = routers.DefaultRouter()

router.register(r"auth", AuthViewSet, basename="auth")
router.register(r"bo_users", BOUserViewSet, basename="bo_users")
router.register(r"categories", CategoryViewSet, basename="categories")
router.register(r"products", ProductViewSet, basename="products")
router.register(r"services", ServiceViewSet, basename="services")
router.register(r"locations", LocationViewSet, basename="locations")

urlpatterns = [
    path("", include(router.urls)),
]
