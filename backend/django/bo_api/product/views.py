from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response

from common.drf import BackofficeViewSetMixin
from hatterparty.models import Product
from .serializers import ProductSerializer, ProductListSerializer


class ProductViewSet(BackofficeViewSetMixin, viewsets.ModelViewSet):
    """
    ViewSet for managing products.
    Provides CRUD operations for products with filtering and search.
    """
    queryset = Product.objects.select_related('category').all()
    serializer_class = ProductSerializer
    filterset_fields = [
        'category',
        'is_active',
        'quantity_available',
    ]
    search_fields = ['name', 'description', 'short_description', 'sku']
    ordering_fields = ['name', 'price', 'quantity_available', 'created_at']
    ordering = ['name']

    def get_serializer_class(self):
        """Use simplified serializer for list actions."""
        if self.action == 'list':
            return ProductListSerializer
        return ProductSerializer

    def get_queryset(self):
        """Filter queryset based on query parameters."""
        queryset = super().get_queryset()
        
        # Filter by availability status
        available = self.request.query_params.get('available')
        if available is not None:
            if available.lower() in ['true', '1']:
                queryset = queryset.filter(is_active=True)
            elif available.lower() in ['false', '0']:
                queryset = queryset.filter(is_active=False)
        
        # Filter by price range
        min_price = self.request.query_params.get('min_price')
        max_price = self.request.query_params.get('max_price')
        if min_price:
            queryset = queryset.filter(price__gte=min_price)
        if max_price:
            queryset = queryset.filter(price__lte=max_price)
        
        return queryset

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate a product."""
        product = self.get_object()
        product.activate()
        product.save()
        return Response({
            'status': 'activated',
            'message': f'Product "{product.name}" has been activated.'
        })

    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """Deactivate a product."""
        product = self.get_object()
        product.deactivate()
        product.save()
        return Response({
            'status': 'deactivated',
            'message': f'Product "{product.name}" has been deactivated.'
        })

    @action(detail=False, methods=['post'])
    def bulk_activate(self, request):
        """Bulk activate products."""
        product_ids = request.data.get('product_ids', [])
        if not product_ids:
            return Response(
                {'error': 'No product IDs provided.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        products = Product.objects.filter(id__in=product_ids)
        count = 0
        for product in products:
            product.activate()
            product.save()
            count += 1
        
        return Response({
            'status': 'success',
            'message': f'Activated {count} products.'
        })

    @action(detail=False, methods=['post'])
    def bulk_deactivate(self, request):
        """Bulk deactivate products."""
        product_ids = request.data.get('product_ids', [])
        if not product_ids:
            return Response(
                {'error': 'No product IDs provided.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        products = Product.objects.filter(id__in=product_ids)
        count = 0
        for product in products:
            product.deactivate()
            product.save()
            count += 1
        
        return Response({
            'status': 'success',
            'message': f'Deactivated {count} products.'
        })
