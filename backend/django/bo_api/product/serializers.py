from rest_framework import serializers
from decimal import Decimal

from hatterparty.models import Product


class ProductSerializer(serializers.ModelSerializer):
    """
    Serializer for Product model.
    """
    category_name = serializers.CharField(source='category.name', read_only=True)
    availability_status = serializers.Char<PERSON>ield(read_only=True)
    is_available = serializers.BooleanField(read_only=True)
    is_currently_valid = serializers.BooleanField(read_only=True)
    activation_status = serializers.CharField(read_only=True)

    class Meta:
        model = Product
        fields = [
            'id',
            'name',
            'description',
            'short_description',
            'price',
            'quantity_available',
            'category',
            'category_name',
            'image',
            'sku',
            'weight',
            'dimensions',
            'valid_from',
            'valid_until',
            'is_active',
            'activated_at',
            'deactivated_at',
            'availability_status',
            'is_available',
            'is_currently_valid',
            'activation_status',
            'created_at',
            'modified_at',
        ]
        read_only_fields = [
            'id',
            'category_name',
            'availability_status',
            'is_available',
            'is_currently_valid',
            'activation_status',
            'activated_at',
            'deactivated_at',
            'created_at',
            'modified_at',
        ]

    def validate_price(self, value):
        """Validate that price is not negative."""
        if value < Decimal('0.00'):
            raise serializers.ValidationError("Price cannot be negative.")
        return value

    def validate_quantity_available(self, value):
        """Validate that quantity is not negative."""
        if value < 0:
            raise serializers.ValidationError("Quantity available cannot be negative.")
        return value

    def validate_weight(self, value):
        """Validate that weight is not negative."""
        if value is not None and value < Decimal('0.00'):
            raise serializers.ValidationError("Weight cannot be negative.")
        return value

    def validate(self, data):
        """Validate temporal validity dates."""
        valid_from = data.get('valid_from')
        valid_until = data.get('valid_until')
        
        if valid_from and valid_until and valid_from >= valid_until:
            raise serializers.ValidationError({
                'valid_until': 'Valid until date must be after valid from date.'
            })
        
        return data


class ProductListSerializer(serializers.ModelSerializer):
    """Simplified Product serializer for list views."""
    category_name = serializers.CharField(source='category.name', read_only=True)
    availability_status = serializers.CharField(read_only=True)
    
    class Meta:
        model = Product
        fields = [
            'id',
            'name',
            'short_description',
            'price',
            'quantity_available',
            'category_name',
            'availability_status',
            'is_active',
            'image',
        ]
