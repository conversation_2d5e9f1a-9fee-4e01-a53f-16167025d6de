from rest_framework import serializers
from decimal import Decimal

from hatterparty.models import Category, Product, Service, Location


class CategorySerializer(serializers.ModelSerializer):
    """
    Serializer for Category model.
    """
    product_count = serializers.SerializerMethodField()
    service_count = serializers.SerializerMethodField()
    location_count = serializers.SerializerMethodField()

    class Meta:
        model = Category
        fields = [
            'id',
            'name',
            'description',
            'slug',
            'product_count',
            'service_count',
            'location_count',
            'created_at',
            'modified_at',
        ]
        read_only_fields = ['id', 'created_at', 'modified_at', 'product_count', 'service_count', 'location_count']

    def get_product_count(self, obj):
        """Get the number of products in this category."""
        return obj.products.count()

    def get_service_count(self, obj):
        """Get the number of services in this category."""
        return obj.services.count()

    def get_location_count(self, obj):
        """Get the number of locations in this category."""
        return obj.locations.count()


class ProductSerializer(serializers.ModelSerializer):
    """
    Serializer for Product model.
    """
    category_name = serializers.CharField(source='category.name', read_only=True)
    availability_status = serializers.CharField(read_only=True)
    is_available = serializers.BooleanField(read_only=True)
    is_currently_valid = serializers.BooleanField(read_only=True)
    activation_status = serializers.CharField(read_only=True)

    class Meta:
        model = Product
        fields = [
            'id',
            'name',
            'description',
            'short_description',
            'price',
            'quantity_available',
            'category',
            'category_name',
            'image',
            'sku',
            'weight',
            'dimensions',
            'valid_from',
            'valid_until',
            'is_active',
            'activated_at',
            'deactivated_at',
            'availability_status',
            'is_available',
            'is_currently_valid',
            'activation_status',
            'created_at',
            'modified_at',
        ]
        read_only_fields = [
            'id',
            'category_name',
            'availability_status',
            'is_available',
            'is_currently_valid',
            'activation_status',
            'activated_at',
            'deactivated_at',
            'created_at',
            'modified_at',
        ]

    def validate_price(self, value):
        """Validate that price is not negative."""
        if value < Decimal('0.00'):
            raise serializers.ValidationError("Price cannot be negative.")
        return value

    def validate_quantity_available(self, value):
        """Validate that quantity is not negative."""
        if value < 0:
            raise serializers.ValidationError("Quantity available cannot be negative.")
        return value

    def validate_weight(self, value):
        """Validate that weight is not negative."""
        if value is not None and value < Decimal('0.00'):
            raise serializers.ValidationError("Weight cannot be negative.")
        return value

    def validate(self, data):
        """Validate temporal validity dates."""
        valid_from = data.get('valid_from')
        valid_until = data.get('valid_until')
        
        if valid_from and valid_until and valid_from >= valid_until:
            raise serializers.ValidationError({
                'valid_until': 'Valid until date must be after valid from date.'
            })
        
        return data


class ServiceSerializer(serializers.ModelSerializer):
    """
    Serializer for Service model.
    """
    category_name = serializers.CharField(source='category.name', read_only=True)
    availability_status = serializers.CharField(read_only=True)
    is_available = serializers.BooleanField(read_only=True)
    is_currently_valid = serializers.BooleanField(read_only=True)
    activation_status = serializers.CharField(read_only=True)

    class Meta:
        model = Service
        fields = [
            'id',
            'name',
            'description',
            'short_description',
            'price',
            'duration_hours',
            'category',
            'category_name',
            'image',
            'max_participants',
            'requires_equipment',
            'valid_from',
            'valid_until',
            'is_active',
            'activated_at',
            'deactivated_at',
            'availability_status',
            'is_available',
            'is_currently_valid',
            'activation_status',
            'created_at',
            'modified_at',
        ]
        read_only_fields = [
            'id',
            'category_name',
            'availability_status',
            'is_available',
            'is_currently_valid',
            'activation_status',
            'activated_at',
            'deactivated_at',
            'created_at',
            'modified_at',
        ]

    def validate_price(self, value):
        """Validate that price is not negative."""
        if value < Decimal('0.00'):
            raise serializers.ValidationError("Price cannot be negative.")
        return value

    def validate_duration_hours(self, value):
        """Validate that duration is positive."""
        if value is not None and value <= 0:
            raise serializers.ValidationError("Duration must be positive.")
        return value

    def validate_max_participants(self, value):
        """Validate that max participants is positive."""
        if value is not None and value <= 0:
            raise serializers.ValidationError("Max participants must be positive.")
        return value

    def validate(self, data):
        """Validate temporal validity dates."""
        valid_from = data.get('valid_from')
        valid_until = data.get('valid_until')
        
        if valid_from and valid_until and valid_from >= valid_until:
            raise serializers.ValidationError({
                'valid_until': 'Valid until date must be after valid from date.'
            })
        
        return data


class LocationSerializer(serializers.ModelSerializer):
    """
    Serializer for Location model.
    """
    category_name = serializers.CharField(source='category.name', read_only=True)
    availability_status = serializers.CharField(read_only=True)
    is_available = serializers.BooleanField(read_only=True)
    is_currently_valid = serializers.BooleanField(read_only=True)
    activation_status = serializers.CharField(read_only=True)
    full_address = serializers.CharField(read_only=True)

    class Meta:
        model = Location
        fields = [
            'id',
            'name',
            'description',
            'short_description',
            'price',
            'address',
            'city',
            'postal_code',
            'full_address',
            'capacity',
            'category',
            'category_name',
            'image',
            'has_parking',
            'has_catering',
            'indoor',
            'outdoor',
            'valid_from',
            'valid_until',
            'is_active',
            'activated_at',
            'deactivated_at',
            'availability_status',
            'is_available',
            'is_currently_valid',
            'activation_status',
            'created_at',
            'modified_at',
        ]
        read_only_fields = [
            'id',
            'category_name',
            'full_address',
            'availability_status',
            'is_available',
            'is_currently_valid',
            'activation_status',
            'activated_at',
            'deactivated_at',
            'created_at',
            'modified_at',
        ]

    def validate_price(self, value):
        """Validate that price is not negative."""
        if value < Decimal('0.00'):
            raise serializers.ValidationError("Price cannot be negative.")
        return value

    def validate_capacity(self, value):
        """Validate that capacity is positive."""
        if value <= 0:
            raise serializers.ValidationError("Capacity must be positive.")
        return value

    def validate(self, data):
        """Validate temporal validity dates."""
        valid_from = data.get('valid_from')
        valid_until = data.get('valid_until')
        
        if valid_from and valid_until and valid_from >= valid_until:
            raise serializers.ValidationError({
                'valid_until': 'Valid until date must be after valid from date.'
            })
        
        return data


# Simplified serializers for list views (better performance)
class CategoryListSerializer(serializers.ModelSerializer):
    """Simplified Category serializer for list views."""
    
    class Meta:
        model = Category
        fields = ['id', 'name', 'slug']


class ProductListSerializer(serializers.ModelSerializer):
    """Simplified Product serializer for list views."""
    category_name = serializers.CharField(source='category.name', read_only=True)
    availability_status = serializers.CharField(read_only=True)
    
    class Meta:
        model = Product
        fields = [
            'id',
            'name',
            'short_description',
            'price',
            'quantity_available',
            'category_name',
            'availability_status',
            'is_active',
            'image',
        ]


class ServiceListSerializer(serializers.ModelSerializer):
    """Simplified Service serializer for list views."""
    category_name = serializers.CharField(source='category.name', read_only=True)
    availability_status = serializers.CharField(read_only=True)
    
    class Meta:
        model = Service
        fields = [
            'id',
            'name',
            'short_description',
            'price',
            'duration_hours',
            'category_name',
            'availability_status',
            'is_active',
            'image',
        ]


class LocationListSerializer(serializers.ModelSerializer):
    """Simplified Location serializer for list views."""
    category_name = serializers.CharField(source='category.name', read_only=True)
    availability_status = serializers.CharField(read_only=True)
    
    class Meta:
        model = Location
        fields = [
            'id',
            'name',
            'short_description',
            'price',
            'city',
            'capacity',
            'category_name',
            'availability_status',
            'is_active',
            'image',
        ]
