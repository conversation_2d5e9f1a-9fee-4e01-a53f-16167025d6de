from rest_framework import viewsets

from common.drf import BackofficeViewSetMixin
from hatterparty.models import BOUser

from .serializers import BOUserSerializer


class BOUserViewSet(BackofficeViewSetMixin, viewsets.ModelViewSet):
    serializer_class = BOUserSerializer
    ordering_fields = ["first_name"]

    def get_queryset(self):
        qs = BOUser.objects.all()
        if self.action not in ["list", "retrieve"]:
            return qs.filter(pk=self.request.user.pk)
        return qs

    def get_object(self):
        if self.kwargs["pk"] == "self":
            return self.request.bo_user
        return super().get_object()
