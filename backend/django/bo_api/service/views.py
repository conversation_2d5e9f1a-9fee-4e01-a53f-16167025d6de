from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response

from common.drf import BackofficeViewSetMixin
from hatterparty.models import Service
from .serializers import ServiceSerializer, ServiceListSerializer


class ServiceViewSet(BackofficeViewSetMixin, viewsets.ModelViewSet):
    """
    ViewSet for managing services.
    Provides CRUD operations for services with filtering and search.
    """
    queryset = Service.objects.select_related('category').all()
    serializer_class = ServiceSerializer
    filterset_fields = [
        'category',
        'is_active',
        'requires_equipment',
    ]
    search_fields = ['name', 'description', 'short_description']
    ordering_fields = ['name', 'price', 'duration_hours', 'created_at']
    ordering = ['name']

    def get_serializer_class(self):
        """Use simplified serializer for list actions."""
        if self.action == 'list':
            return ServiceListSerializer
        return ServiceSerializer

    def get_queryset(self):
        """Filter queryset based on query parameters."""
        queryset = super().get_queryset()
        
        # Filter by availability status
        available = self.request.query_params.get('available')
        if available is not None:
            if available.lower() in ['true', '1']:
                queryset = queryset.filter(is_active=True)
            elif available.lower() in ['false', '0']:
                queryset = queryset.filter(is_active=False)
        
        # Filter by price range
        min_price = self.request.query_params.get('min_price')
        max_price = self.request.query_params.get('max_price')
        if min_price:
            queryset = queryset.filter(price__gte=min_price)
        if max_price:
            queryset = queryset.filter(price__lte=max_price)
        
        # Filter by duration range
        min_duration = self.request.query_params.get('min_duration')
        max_duration = self.request.query_params.get('max_duration')
        if min_duration:
            queryset = queryset.filter(duration_hours__gte=min_duration)
        if max_duration:
            queryset = queryset.filter(duration_hours__lte=max_duration)
        
        return queryset

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate a service."""
        service = self.get_object()
        service.activate()
        service.save()
        return Response({
            'status': 'activated',
            'message': f'Service "{service.name}" has been activated.'
        })

    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """Deactivate a service."""
        service = self.get_object()
        service.deactivate()
        service.save()
        return Response({
            'status': 'deactivated',
            'message': f'Service "{service.name}" has been deactivated.'
        })

    @action(detail=False, methods=['post'])
    def bulk_activate(self, request):
        """Bulk activate services."""
        service_ids = request.data.get('service_ids', [])
        if not service_ids:
            return Response(
                {'error': 'No service IDs provided.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        services = Service.objects.filter(id__in=service_ids)
        count = 0
        for service in services:
            service.activate()
            service.save()
            count += 1
        
        return Response({
            'status': 'success',
            'message': f'Activated {count} services.'
        })

    @action(detail=False, methods=['post'])
    def bulk_deactivate(self, request):
        """Bulk deactivate services."""
        service_ids = request.data.get('service_ids', [])
        if not service_ids:
            return Response(
                {'error': 'No service IDs provided.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        services = Service.objects.filter(id__in=service_ids)
        count = 0
        for service in services:
            service.deactivate()
            service.save()
            count += 1
        
        return Response({
            'status': 'success',
            'message': f'Deactivated {count} services.'
        })
