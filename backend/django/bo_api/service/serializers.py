from rest_framework import serializers
from decimal import Decimal

from hatterparty.models import Service


class ServiceSerializer(serializers.ModelSerializer):
    """
    Serializer for Service model.
    """
    category_name = serializers.CharField(source='category.name', read_only=True)
    availability_status = serializers.Char<PERSON>ield(read_only=True)
    is_available = serializers.BooleanField(read_only=True)
    is_currently_valid = serializers.BooleanField(read_only=True)
    activation_status = serializers.CharField(read_only=True)

    class Meta:
        model = Service
        fields = [
            'id',
            'name',
            'description',
            'short_description',
            'price',
            'duration_hours',
            'category',
            'category_name',
            'image',
            'max_participants',
            'requires_equipment',
            'valid_from',
            'valid_until',
            'is_active',
            'activated_at',
            'deactivated_at',
            'availability_status',
            'is_available',
            'is_currently_valid',
            'activation_status',
            'created_at',
            'modified_at',
        ]
        read_only_fields = [
            'id',
            'category_name',
            'availability_status',
            'is_available',
            'is_currently_valid',
            'activation_status',
            'activated_at',
            'deactivated_at',
            'created_at',
            'modified_at',
        ]

    def validate_price(self, value):
        """Validate that price is not negative."""
        if value < Decimal('0.00'):
            raise serializers.ValidationError("Price cannot be negative.")
        return value

    def validate_duration_hours(self, value):
        """Validate that duration is positive."""
        if value is not None and value <= 0:
            raise serializers.ValidationError("Duration must be positive.")
        return value

    def validate_max_participants(self, value):
        """Validate that max participants is positive."""
        if value is not None and value <= 0:
            raise serializers.ValidationError("Max participants must be positive.")
        return value

    def validate(self, data):
        """Validate temporal validity dates."""
        valid_from = data.get('valid_from')
        valid_until = data.get('valid_until')
        
        if valid_from and valid_until and valid_from >= valid_until:
            raise serializers.ValidationError({
                'valid_until': 'Valid until date must be after valid from date.'
            })
        
        return data


class ServiceListSerializer(serializers.ModelSerializer):
    """Simplified Service serializer for list views."""
    category_name = serializers.CharField(source='category.name', read_only=True)
    availability_status = serializers.CharField(read_only=True)
    
    class Meta:
        model = Service
        fields = [
            'id',
            'name',
            'short_description',
            'price',
            'duration_hours',
            'category_name',
            'availability_status',
            'is_active',
            'image',
        ]
