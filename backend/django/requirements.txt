# This file was autogenerated by uv via the following command:
#    uv pip compile requirements.in --unsafe-package setuptools -o requirements.txt
asgiref==3.8.1
    # via
    #   django
    #   django-cors-headers
asn1crypto==1.5.1
    # via -r requirements.in
bcrypt==4.3.0
    # via paramiko
cffi==1.17.1
    # via
    #   cryptography
    #   pynacl
cryptography==44.0.3
    # via paramiko
django==5.1.5
    # via
    #   -r requirements.in
    #   django-cors-headers
    #   django-extensions
    #   django-filter
    #   djangorestframework
django-cors-headers==4.7.0
    # via -r requirements.in
django-extensions==3.2.3
    # via -r requirements.in
django-filter==25.1
    # via -r requirements.in
djangorestframework==3.15.2
    # via -r requirements.in
elementpath==4.8.0
    # via xmlschema
et-xmlfile==2.0.0
    # via openpyxl
factory-boy==3.3.3
    # via -r requirements.in
faker==37.0.2
    # via factory-boy
iniconfig==2.0.0
    # via pytest
openpyxl==3.1.5
    # via -r requirements.in
packaging==24.2
    # via pytest
paramiko==3.5.1
    # via -r requirements.in
pillow==11.3.0
    # via -r requirements.in
pluggy==1.5.0
    # via pytest
psycopg2-binary==2.9.10
    # via -r requirements.in
pycparser==2.22
    # via cffi
pynacl==1.5.0
    # via paramiko
pytest==8.3.4
    # via pytest-django
pytest-django==4.10.0
    # via -r requirements.in
sqlparse==0.5.3
    # via django
tzdata==2025.1
    # via faker
xmlschema==3.4.5
    # via -r requirements.in
