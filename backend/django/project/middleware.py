from django.conf import settings

from hatterparty.models import BOUser


class SessionError(Exception):
    pass


def user_from_session(get_response):
    def middleware(request):
        request.bo_user = None
        if settings.IN_TEST:
            request.bo_user = request._force_auth_user  # noqa: SLF001
        else:
            try:
                bo_user = BOUser.objects.get(pk=request.session["bo_user_id"])
                if bo_user.is_active:
                    request.bo_user = bo_user
                else:
                    del request.session["bo_user_id"]
            except:  # noqa: E722 S110
                pass
        return get_response(request)

    return middleware
