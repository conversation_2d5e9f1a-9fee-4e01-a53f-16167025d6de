from .base import *  # noqa: F403

BACKOFFICE_BASE_URL = "http://backoffice.hatterparty.localhost"

DEBUG = True
ALLOWED_HOSTS = ["0.0.0.0", "backend.hatterparty.localhost"]  # noqa: S104

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "hatterparty",
        "USER": "hatterparty",
        "PASSWORD": "hatterparty",
        "HOST": "hatterparty_db",
        "PORT": "5432",
    }
}

CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [BACKOFFICE_BASE_URL]
