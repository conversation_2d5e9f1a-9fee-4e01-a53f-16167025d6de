# Generated by Django 5.1.5 on 2025-08-29 14:34

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hatterparty', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Data creazione')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Data ultima modifica')),
                ('name', models.CharField(help_text='Category name', max_length=100, unique=True, verbose_name='Name')),
                ('description', models.TextField(blank=True, help_text='Category description', verbose_name='Description')),
                ('slug', models.SlugField(help_text='URL-friendly category identifier', max_length=100, unique=True, verbose_name='Slug')),
            ],
            options={
                'verbose_name': 'Category',
                'verbose_name_plural': 'Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Data creazione')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Data ultima modifica')),
                ('valid_from', models.DateTimeField(blank=True, help_text='Start date/time when this record becomes valid', null=True, verbose_name='Valid from')),
                ('valid_until', models.DateTimeField(blank=True, help_text='End date/time when this record expires', null=True, verbose_name='Valid until')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this record is active', verbose_name='Active')),
                ('activated_at', models.DateTimeField(blank=True, help_text='When this record was activated', null=True, verbose_name='Activated at')),
                ('deactivated_at', models.DateTimeField(blank=True, help_text='When this record was deactivated', null=True, verbose_name='Deactivated at')),
                ('name', models.CharField(help_text='Location name', max_length=200, verbose_name='Name')),
                ('description', models.TextField(blank=True, help_text='Detailed location description', verbose_name='Description')),
                ('short_description', models.CharField(blank=True, help_text='Brief location description for listings', max_length=500, verbose_name='Short description')),
                ('price', models.DecimalField(decimal_places=2, help_text='Rental price per day', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Price per day')),
                ('address', models.TextField(help_text='Full address of the location', verbose_name='Address')),
                ('city', models.CharField(help_text='City where the location is situated', max_length=100, verbose_name='City')),
                ('postal_code', models.CharField(help_text='Postal code', max_length=20, verbose_name='Postal code')),
                ('capacity', models.PositiveIntegerField(help_text='Maximum number of people the location can accommodate', verbose_name='Capacity')),
                ('image', models.ImageField(blank=True, help_text='Location image', null=True, upload_to='locations/', verbose_name='Image')),
                ('has_parking', models.BooleanField(default=False, help_text='Whether the location has parking available', verbose_name='Has parking')),
                ('has_catering', models.BooleanField(default=False, help_text='Whether the location provides catering services', verbose_name='Has catering')),
                ('indoor', models.BooleanField(default=True, help_text='Whether the location is indoor', verbose_name='Indoor')),
                ('outdoor', models.BooleanField(default=False, help_text='Whether the location has outdoor space', verbose_name='Outdoor')),
                ('category', models.ForeignKey(blank=True, help_text='Location category', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='locations', to='hatterparty.category', verbose_name='Category')),
            ],
            options={
                'verbose_name': 'Location',
                'verbose_name_plural': 'Locations',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Data creazione')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Data ultima modifica')),
                ('valid_from', models.DateTimeField(blank=True, help_text='Start date/time when this record becomes valid', null=True, verbose_name='Valid from')),
                ('valid_until', models.DateTimeField(blank=True, help_text='End date/time when this record expires', null=True, verbose_name='Valid until')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this record is active', verbose_name='Active')),
                ('activated_at', models.DateTimeField(blank=True, help_text='When this record was activated', null=True, verbose_name='Activated at')),
                ('deactivated_at', models.DateTimeField(blank=True, help_text='When this record was deactivated', null=True, verbose_name='Deactivated at')),
                ('name', models.CharField(help_text='Product name', max_length=200, verbose_name='Name')),
                ('description', models.TextField(blank=True, help_text='Detailed product description', verbose_name='Description')),
                ('short_description', models.CharField(blank=True, help_text='Brief product description for listings', max_length=500, verbose_name='Short description')),
                ('price', models.DecimalField(decimal_places=2, help_text='Rental price per unit', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Price')),
                ('quantity_available', models.PositiveIntegerField(default=1, help_text='Number of units available for rent', verbose_name='Quantity available')),
                ('image', models.ImageField(blank=True, help_text='Product image', null=True, upload_to='products/', verbose_name='Image')),
                ('sku', models.CharField(blank=True, help_text='Stock Keeping Unit identifier', max_length=50, null=True, unique=True, verbose_name='SKU')),
                ('weight', models.DecimalField(blank=True, decimal_places=2, help_text='Product weight in kilograms', max_digits=8, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Weight (kg)')),
                ('dimensions', models.CharField(blank=True, help_text='Product dimensions (e.g., 100x50x30 cm)', max_length=100, verbose_name='Dimensions')),
                ('category', models.ForeignKey(blank=True, help_text='Product category', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='hatterparty.category', verbose_name='Category')),
            ],
            options={
                'verbose_name': 'Product',
                'verbose_name_plural': 'Products',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Data creazione')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Data ultima modifica')),
                ('valid_from', models.DateTimeField(blank=True, help_text='Start date/time when this record becomes valid', null=True, verbose_name='Valid from')),
                ('valid_until', models.DateTimeField(blank=True, help_text='End date/time when this record expires', null=True, verbose_name='Valid until')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this record is active', verbose_name='Active')),
                ('activated_at', models.DateTimeField(blank=True, help_text='When this record was activated', null=True, verbose_name='Activated at')),
                ('deactivated_at', models.DateTimeField(blank=True, help_text='When this record was deactivated', null=True, verbose_name='Deactivated at')),
                ('name', models.CharField(help_text='Service name', max_length=200, verbose_name='Name')),
                ('description', models.TextField(blank=True, help_text='Detailed service description', verbose_name='Description')),
                ('short_description', models.CharField(blank=True, help_text='Brief service description for listings', max_length=500, verbose_name='Short description')),
                ('price', models.DecimalField(decimal_places=2, help_text='Service price', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))], verbose_name='Price')),
                ('duration_hours', models.PositiveIntegerField(blank=True, help_text='Service duration in hours', null=True, verbose_name='Duration (hours)')),
                ('image', models.ImageField(blank=True, help_text='Service image', null=True, upload_to='services/', verbose_name='Image')),
                ('max_participants', models.PositiveIntegerField(blank=True, help_text='Maximum number of participants for this service', null=True, verbose_name='Max participants')),
                ('requires_equipment', models.BooleanField(default=False, help_text='Whether this service requires special equipment', verbose_name='Requires equipment')),
                ('category', models.ForeignKey(blank=True, help_text='Service category', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='services', to='hatterparty.category', verbose_name='Category')),
            ],
            options={
                'verbose_name': 'Service',
                'verbose_name_plural': 'Services',
                'ordering': ['name'],
            },
        ),
    ]
