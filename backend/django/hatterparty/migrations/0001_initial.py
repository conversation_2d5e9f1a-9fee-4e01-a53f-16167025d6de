# Generated by Django 5.1.5 on 2025-03-12 22:20

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BOUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Data creazione')),
                ('modified_at', models.DateTimeField(auto_now=True, verbose_name='Data ultima modifica')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('password_hash', models.Char<PERSON>ield(blank=True, max_length=255)),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='Ultimo accesso')),
                ('is_active', models.BooleanField(default=True, verbose_name='Attivo')),
                ('first_name', models.Char<PERSON><PERSON>(blank=True, verbose_name='Nome')),
                ('last_name', models.Char<PERSON>ield(blank=True, verbose_name='Cognome')),
            ],
            options={
                'verbose_name': 'Utente di Backoffice',
                'verbose_name_plural': 'Utenti di Backoffice',
            },
        ),
    ]
