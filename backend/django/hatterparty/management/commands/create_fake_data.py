from django.conf import settings
from django.core.management import BaseCommand, CommandError

from hatterparty.factories.bo_user import BOUserFactory

FAKER_PASSWORD = "hatterparty"  # noqa: S105


class Command(BaseCommand):
    help = "Create fake data"

    def handle(self, *args, **options):
        if settings.IN_PROD:
            raise CommandError("Open your eyes! You're in production...")

        self.create_bo_users(count=10)

    def create_bo_users(self, *, count):
        for index in range(1, count + 1):
            BOUserFactory(email=f"bouser{index}@hatterparty.it", password=FAKER_PASSWORD)
