from decimal import Decimal
from django.test import TestCase
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from hatterparty.models import Category, Service


class ServiceModelTest(TestCase):
    """Test cases for Service model."""

    def setUp(self):
        self.category = Category.objects.create(
            name='Service Category',
            slug='service-category'
        )
        self.service_data = {
            'name': 'Test Service',
            'description': 'Test service description',
            'short_description': 'Short service description',
            'price': Decimal('150.00'),
            'duration_hours': 4,
            'category': self.category,
            'max_participants': 20,
            'requires_equipment': True,
        }

    def test_create_service(self):
        """Test creating a service."""
        service = Service.objects.create(**self.service_data)
        self.assertEqual(service.name, 'Test Service')
        self.assertEqual(service.price, Decimal('150.00'))
        self.assertEqual(service.duration_hours, 4)
        self.assertEqual(service.max_participants, 20)
        self.assertTrue(service.requires_equipment)
        self.assertTrue(service.is_active)

    def test_service_str_representation(self):
        """Test service string representation."""
        service = Service.objects.create(**self.service_data)
        self.assertEqual(str(service), 'Test Service')

    def test_service_is_available_property(self):
        """Test service is_available property."""
        service = Service.objects.create(**self.service_data)
        self.assertTrue(service.is_available)

        # Test inactive service
        service.is_active = False
        service.save()
        self.assertFalse(service.is_available)

    def test_service_availability_status_property(self):
        """Test service availability_status property."""
        service = Service.objects.create(**self.service_data)
        self.assertEqual(service.availability_status, 'Available')

        # Test inactive service
        service.is_active = False
        service.save()
        self.assertEqual(service.availability_status, 'Inactive')

    def test_service_temporal_validity(self):
        """Test service temporal validity functionality."""
        now = timezone.now()
        future = now + timedelta(days=30)
        past = now - timedelta(days=30)

        # Test valid service
        service = Service.objects.create(
            **self.service_data,
            valid_from=past,
            valid_until=future
        )
        self.assertTrue(service.is_currently_valid)
        self.assertTrue(service.is_valid_at(now))

        # Test expired service
        service.valid_until = past
        service.save()
        self.assertFalse(service.is_currently_valid)
        self.assertFalse(service.is_valid_at(now))

    def test_service_activation_methods(self):
        """Test service activation and deactivation methods."""
        service = Service.objects.create(**self.service_data)
        
        # Test deactivation
        service.deactivate()
        self.assertFalse(service.is_active)
        self.assertTrue(service.deactivated_at)
        self.assertEqual(service.activation_status, 'Inactive')

        # Test activation
        service.activate()
        self.assertTrue(service.is_active)
        self.assertTrue(service.activated_at)
        self.assertIsNone(service.deactivated_at)
        self.assertEqual(service.activation_status, 'Active')
