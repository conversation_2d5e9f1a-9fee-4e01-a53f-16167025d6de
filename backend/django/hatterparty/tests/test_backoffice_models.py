from decimal import Decimal
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from hatterparty.models import Category, Product, Service, Location


class CategoryModelTest(TestCase):
    """Test cases for Category model."""

    def setUp(self):
        self.category_data = {
            'name': 'Test Category',
            'description': 'Test category description',
            'slug': 'test-category',
        }

    def test_create_category(self):
        """Test creating a category."""
        category = Category.objects.create(**self.category_data)
        self.assertEqual(category.name, 'Test Category')
        self.assertEqual(category.slug, 'test-category')
        self.assertTrue(category.created_at)
        self.assertTrue(category.modified_at)

    def test_category_str_representation(self):
        """Test category string representation."""
        category = Category.objects.create(**self.category_data)
        self.assertEqual(str(category), 'Test Category')

    def test_category_unique_name(self):
        """Test that category names must be unique."""
        Category.objects.create(**self.category_data)
        with self.assertRaises(Exception):  # IntegrityError
            Category.objects.create(**self.category_data)

    def test_category_unique_slug(self):
        """Test that category slugs must be unique."""
        Category.objects.create(**self.category_data)
        duplicate_data = self.category_data.copy()
        duplicate_data['name'] = 'Different Name'
        with self.assertRaises(Exception):  # IntegrityError
            Category.objects.create(**duplicate_data)


class ProductModelTest(TestCase):
    """Test cases for Product model."""

    def setUp(self):
        self.category = Category.objects.create(
            name='Test Category',
            slug='test-category'
        )
        self.product_data = {
            'name': 'Test Product',
            'description': 'Test product description',
            'short_description': 'Short description',
            'price': Decimal('99.99'),
            'quantity_available': 10,
            'category': self.category,
            'sku': 'TEST-001',
            'weight': Decimal('5.50'),
            'dimensions': '100x50x30 cm',
        }

    def test_create_product(self):
        """Test creating a product."""
        product = Product.objects.create(**self.product_data)
        self.assertEqual(product.name, 'Test Product')
        self.assertEqual(product.price, Decimal('99.99'))
        self.assertEqual(product.quantity_available, 10)
        self.assertEqual(product.category, self.category)
        self.assertTrue(product.is_active)
        self.assertTrue(product.created_at)

    def test_product_str_representation(self):
        """Test product string representation."""
        product = Product.objects.create(**self.product_data)
        self.assertEqual(str(product), 'Test Product')

    def test_product_is_available_property(self):
        """Test product is_available property."""
        product = Product.objects.create(**self.product_data)
        self.assertTrue(product.is_available)

        # Test inactive product
        product.is_active = False
        product.save()
        self.assertFalse(product.is_available)

    def test_product_availability_status_property(self):
        """Test product availability_status property."""
        product = Product.objects.create(**self.product_data)
        self.assertIn('available', product.availability_status)

        # Test inactive product
        product.is_active = False
        product.save()
        self.assertEqual(product.availability_status, 'Inactive')

        # Test out of stock
        product.is_active = True
        product.quantity_available = 0
        product.save()
        self.assertEqual(product.availability_status, 'Out of stock')

    def test_product_temporal_validity(self):
        """Test product temporal validity functionality."""
        now = timezone.now()
        future = now + timedelta(days=30)
        past = now - timedelta(days=30)

        # Test valid product
        product = Product.objects.create(
            **self.product_data,
            valid_from=past,
            valid_until=future
        )
        self.assertTrue(product.is_currently_valid)
        self.assertTrue(product.is_valid_at(now))

        # Test expired product
        product.valid_until = past
        product.save()
        self.assertFalse(product.is_currently_valid)
        self.assertFalse(product.is_valid_at(now))

    def test_product_activation_methods(self):
        """Test product activation and deactivation methods."""
        product = Product.objects.create(**self.product_data)
        
        # Test deactivation
        product.deactivate()
        self.assertFalse(product.is_active)
        self.assertTrue(product.deactivated_at)
        self.assertEqual(product.activation_status, 'Inactive')

        # Test activation
        product.activate()
        self.assertTrue(product.is_active)
        self.assertTrue(product.activated_at)
        self.assertIsNone(product.deactivated_at)
        self.assertEqual(product.activation_status, 'Active')


class ServiceModelTest(TestCase):
    """Test cases for Service model."""

    def setUp(self):
        self.category = Category.objects.create(
            name='Service Category',
            slug='service-category'
        )
        self.service_data = {
            'name': 'Test Service',
            'description': 'Test service description',
            'short_description': 'Short service description',
            'price': Decimal('150.00'),
            'duration_hours': 4,
            'category': self.category,
            'max_participants': 20,
            'requires_equipment': True,
        }

    def test_create_service(self):
        """Test creating a service."""
        service = Service.objects.create(**self.service_data)
        self.assertEqual(service.name, 'Test Service')
        self.assertEqual(service.price, Decimal('150.00'))
        self.assertEqual(service.duration_hours, 4)
        self.assertEqual(service.max_participants, 20)
        self.assertTrue(service.requires_equipment)
        self.assertTrue(service.is_active)

    def test_service_str_representation(self):
        """Test service string representation."""
        service = Service.objects.create(**self.service_data)
        self.assertEqual(str(service), 'Test Service')

    def test_service_is_available_property(self):
        """Test service is_available property."""
        service = Service.objects.create(**self.service_data)
        self.assertTrue(service.is_available)

        # Test inactive service
        service.is_active = False
        service.save()
        self.assertFalse(service.is_available)

    def test_service_availability_status_property(self):
        """Test service availability_status property."""
        service = Service.objects.create(**self.service_data)
        self.assertEqual(service.availability_status, 'Available')

        # Test inactive service
        service.is_active = False
        service.save()
        self.assertEqual(service.availability_status, 'Inactive')


class LocationModelTest(TestCase):
    """Test cases for Location model."""

    def setUp(self):
        self.category = Category.objects.create(
            name='Location Category',
            slug='location-category'
        )
        self.location_data = {
            'name': 'Test Location',
            'description': 'Test location description',
            'short_description': 'Short location description',
            'price': Decimal('500.00'),
            'address': '123 Test Street',
            'city': 'Test City',
            'postal_code': '12345',
            'capacity': 100,
            'category': self.category,
            'has_parking': True,
            'has_catering': False,
            'indoor': True,
            'outdoor': False,
        }

    def test_create_location(self):
        """Test creating a location."""
        location = Location.objects.create(**self.location_data)
        self.assertEqual(location.name, 'Test Location')
        self.assertEqual(location.price, Decimal('500.00'))
        self.assertEqual(location.capacity, 100)
        self.assertEqual(location.city, 'Test City')
        self.assertTrue(location.has_parking)
        self.assertFalse(location.has_catering)
        self.assertTrue(location.is_active)

    def test_location_str_representation(self):
        """Test location string representation."""
        location = Location.objects.create(**self.location_data)
        self.assertEqual(str(location), 'Test Location - Test City')

    def test_location_full_address_property(self):
        """Test location full_address property."""
        location = Location.objects.create(**self.location_data)
        expected_address = '123 Test Street, Test City 12345'
        self.assertEqual(location.full_address, expected_address)

    def test_location_is_available_property(self):
        """Test location is_available property."""
        location = Location.objects.create(**self.location_data)
        self.assertTrue(location.is_available)

        # Test inactive location
        location.is_active = False
        location.save()
        self.assertFalse(location.is_available)

    def test_location_availability_status_property(self):
        """Test location availability_status property."""
        location = Location.objects.create(**self.location_data)
        self.assertEqual(location.availability_status, 'Available')

        # Test inactive location
        location.is_active = False
        location.save()
        self.assertEqual(location.availability_status, 'Inactive')


class MixinTest(TestCase):
    """Test cases for model mixins."""

    def setUp(self):
        self.product_data = {
            'name': 'Test Product',
            'price': Decimal('99.99'),
            'quantity_available': 10,
        }

    def test_temporal_validity_mixin(self):
        """Test TemporalValidityMixin functionality."""
        now = timezone.now()
        future = now + timedelta(days=30)
        past = now - timedelta(days=30)

        product = Product.objects.create(**self.product_data)

        # Test with no validity dates
        self.assertTrue(product.is_valid_at(now))
        self.assertTrue(product.is_currently_valid)

        # Test with valid_from in future
        product.valid_from = future
        product.save()
        self.assertFalse(product.is_valid_at(now))
        self.assertFalse(product.is_currently_valid)

        # Test with valid_until in past
        product.valid_from = past
        product.valid_until = past
        product.save()
        self.assertFalse(product.is_valid_at(now))
        self.assertFalse(product.is_currently_valid)

        # Test with valid period
        product.valid_from = past
        product.valid_until = future
        product.save()
        self.assertTrue(product.is_valid_at(now))
        self.assertTrue(product.is_currently_valid)

    def test_activation_mixin(self):
        """Test ActivationMixin functionality."""
        product = Product.objects.create(**self.product_data)

        # Test default state
        self.assertTrue(product.is_active)
        self.assertEqual(product.activation_status, 'Active')

        # Test deactivation
        product.deactivate()
        self.assertFalse(product.is_active)
        self.assertTrue(product.deactivated_at)
        self.assertEqual(product.activation_status, 'Inactive')

        # Test activation
        product.activate()
        self.assertTrue(product.is_active)
        self.assertTrue(product.activated_at)
        self.assertIsNone(product.deactivated_at)
        self.assertEqual(product.activation_status, 'Active')
