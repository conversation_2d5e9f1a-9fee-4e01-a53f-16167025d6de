from decimal import Decimal
from django.test import TestCase
from django.utils import timezone
from datetime import timed<PERSON>ta

from hatterparty.models import Category, Product


class ProductModelTest(TestCase):
    """Test cases for Product model."""

    def setUp(self):
        self.category = Category.objects.create(
            name='Test Category',
            slug='test-category'
        )
        self.product_data = {
            'name': 'Test Product',
            'description': 'Test product description',
            'short_description': 'Short description',
            'price': Decimal('99.99'),
            'quantity_available': 10,
            'category': self.category,
            'sku': 'TEST-001',
            'weight': Decimal('5.50'),
            'dimensions': '100x50x30 cm',
        }

    def test_create_product(self):
        """Test creating a product."""
        product = Product.objects.create(**self.product_data)
        self.assertEqual(product.name, 'Test Product')
        self.assertEqual(product.price, Decimal('99.99'))
        self.assertEqual(product.quantity_available, 10)
        self.assertEqual(product.category, self.category)
        self.assertTrue(product.is_active)
        self.assertTrue(product.created_at)

    def test_product_str_representation(self):
        """Test product string representation."""
        product = Product.objects.create(**self.product_data)
        self.assertEqual(str(product), 'Test Product')

    def test_product_is_available_property(self):
        """Test product is_available property."""
        product = Product.objects.create(**self.product_data)
        self.assertTrue(product.is_available)

        # Test inactive product
        product.is_active = False
        product.save()
        self.assertFalse(product.is_available)

    def test_product_availability_status_property(self):
        """Test product availability_status property."""
        product = Product.objects.create(**self.product_data)
        self.assertIn('available', product.availability_status)

        # Test inactive product
        product.is_active = False
        product.save()
        self.assertEqual(product.availability_status, 'Inactive')

        # Test out of stock
        product.is_active = True
        product.quantity_available = 0
        product.save()
        self.assertEqual(product.availability_status, 'Out of stock')

    def test_product_temporal_validity(self):
        """Test product temporal validity functionality."""
        now = timezone.now()
        future = now + timedelta(days=30)
        past = now - timedelta(days=30)

        # Test valid product
        product = Product.objects.create(
            **self.product_data,
            valid_from=past,
            valid_until=future
        )
        self.assertTrue(product.is_currently_valid)
        self.assertTrue(product.is_valid_at(now))

        # Test expired product
        product.valid_until = past
        product.save()
        self.assertFalse(product.is_currently_valid)
        self.assertFalse(product.is_valid_at(now))

    def test_product_activation_methods(self):
        """Test product activation and deactivation methods."""
        product = Product.objects.create(**self.product_data)
        
        # Test deactivation
        product.deactivate()
        self.assertFalse(product.is_active)
        self.assertTrue(product.deactivated_at)
        self.assertEqual(product.activation_status, 'Inactive')

        # Test activation
        product.activate()
        self.assertTrue(product.is_active)
        self.assertTrue(product.activated_at)
        self.assertIsNone(product.deactivated_at)
        self.assertEqual(product.activation_status, 'Active')


class ProductMixinTest(TestCase):
    """Test cases for Product model mixins."""

    def setUp(self):
        self.category = Category.objects.create(
            name='Test Category',
            slug='test-category'
        )
        self.product_data = {
            'name': 'Test Product',
            'price': Decimal('99.99'),
            'quantity_available': 10,
            'category': self.category,
        }

    def test_temporal_validity_mixin(self):
        """Test TemporalValidityMixin functionality."""
        now = timezone.now()
        future = now + timedelta(days=30)
        past = now - timedelta(days=30)

        product = Product.objects.create(**self.product_data)

        # Test with no validity dates
        self.assertTrue(product.is_valid_at(now))
        self.assertTrue(product.is_currently_valid)

        # Test with valid_from in future
        product.valid_from = future
        product.save()
        self.assertFalse(product.is_valid_at(now))
        self.assertFalse(product.is_currently_valid)

        # Test with valid_until in past
        product.valid_from = past
        product.valid_until = past
        product.save()
        self.assertFalse(product.is_valid_at(now))
        self.assertFalse(product.is_currently_valid)

        # Test with valid period
        product.valid_from = past
        product.valid_until = future
        product.save()
        self.assertTrue(product.is_valid_at(now))
        self.assertTrue(product.is_currently_valid)

    def test_activation_mixin(self):
        """Test ActivationMixin functionality."""
        product = Product.objects.create(**self.product_data)

        # Test default state
        self.assertTrue(product.is_active)
        self.assertEqual(product.activation_status, 'Active')

        # Test deactivation
        product.deactivate()
        self.assertFalse(product.is_active)
        self.assertTrue(product.deactivated_at)
        self.assertEqual(product.activation_status, 'Inactive')

        # Test activation
        product.activate()
        self.assertTrue(product.is_active)
        self.assertTrue(product.activated_at)
        self.assertIsNone(product.deactivated_at)
        self.assertEqual(product.activation_status, 'Active')
