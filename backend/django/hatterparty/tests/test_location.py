from decimal import Decimal
from django.test import TestCase
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from hatterparty.models import Category, Location


class LocationModelTest(TestCase):
    """Test cases for Location model."""

    def setUp(self):
        self.category = Category.objects.create(
            name='Location Category',
            slug='location-category'
        )
        self.location_data = {
            'name': 'Test Location',
            'description': 'Test location description',
            'short_description': 'Short location description',
            'price': Decimal('500.00'),
            'address': '123 Test Street',
            'city': 'Test City',
            'postal_code': '12345',
            'capacity': 100,
            'category': self.category,
            'has_parking': True,
            'has_catering': False,
            'indoor': True,
            'outdoor': False,
        }

    def test_create_location(self):
        """Test creating a location."""
        location = Location.objects.create(**self.location_data)
        self.assertEqual(location.name, 'Test Location')
        self.assertEqual(location.price, Decimal('500.00'))
        self.assertEqual(location.capacity, 100)
        self.assertEqual(location.city, 'Test City')
        self.assertTrue(location.has_parking)
        self.assertFalse(location.has_catering)
        self.assertTrue(location.is_active)

    def test_location_str_representation(self):
        """Test location string representation."""
        location = Location.objects.create(**self.location_data)
        self.assertEqual(str(location), 'Test Location - Test City')

    def test_location_full_address_property(self):
        """Test location full_address property."""
        location = Location.objects.create(**self.location_data)
        expected_address = '123 Test Street, Test City 12345'
        self.assertEqual(location.full_address, expected_address)

    def test_location_is_available_property(self):
        """Test location is_available property."""
        location = Location.objects.create(**self.location_data)
        self.assertTrue(location.is_available)

        # Test inactive location
        location.is_active = False
        location.save()
        self.assertFalse(location.is_available)

    def test_location_availability_status_property(self):
        """Test location availability_status property."""
        location = Location.objects.create(**self.location_data)
        self.assertEqual(location.availability_status, 'Available')

        # Test inactive location
        location.is_active = False
        location.save()
        self.assertEqual(location.availability_status, 'Inactive')

    def test_location_temporal_validity(self):
        """Test location temporal validity functionality."""
        now = timezone.now()
        future = now + timedelta(days=30)
        past = now - timedelta(days=30)

        # Test valid location
        location = Location.objects.create(
            **self.location_data,
            valid_from=past,
            valid_until=future
        )
        self.assertTrue(location.is_currently_valid)
        self.assertTrue(location.is_valid_at(now))

        # Test expired location
        location.valid_until = past
        location.save()
        self.assertFalse(location.is_currently_valid)
        self.assertFalse(location.is_valid_at(now))

    def test_location_activation_methods(self):
        """Test location activation and deactivation methods."""
        location = Location.objects.create(**self.location_data)
        
        # Test deactivation
        location.deactivate()
        self.assertFalse(location.is_active)
        self.assertTrue(location.deactivated_at)
        self.assertEqual(location.activation_status, 'Inactive')

        # Test activation
        location.activate()
        self.assertTrue(location.is_active)
        self.assertTrue(location.activated_at)
        self.assertIsNone(location.deactivated_at)
        self.assertEqual(location.activation_status, 'Active')
