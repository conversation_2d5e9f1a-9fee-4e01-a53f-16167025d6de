from django.test import TestCase

from hatterparty.models import Category


class CategoryModelTest(TestCase):
    """Test cases for Category model."""

    def setUp(self):
        self.category_data = {
            'name': 'Test Category',
            'description': 'Test category description',
            'slug': 'test-category',
        }

    def test_create_category(self):
        """Test creating a category."""
        category = Category.objects.create(**self.category_data)
        self.assertEqual(category.name, 'Test Category')
        self.assertEqual(category.slug, 'test-category')
        self.assertTrue(category.created_at)
        self.assertTrue(category.modified_at)

    def test_category_str_representation(self):
        """Test category string representation."""
        category = Category.objects.create(**self.category_data)
        self.assertEqual(str(category), 'Test Category')

    def test_category_unique_name(self):
        """Test that category names must be unique."""
        Category.objects.create(**self.category_data)
        with self.assertRaises(Exception):  # IntegrityError
            Category.objects.create(**self.category_data)

    def test_category_unique_slug(self):
        """Test that category slugs must be unique."""
        Category.objects.create(**self.category_data)
        duplicate_data = self.category_data.copy()
        duplicate_data['name'] = 'Different Name'
        with self.assertRaises(Exception):  # IntegrityError
            Category.objects.create(**duplicate_data)
