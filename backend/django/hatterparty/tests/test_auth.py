import pytest
from django.urls import reverse
from rest_framework.test import APIClient

from hatterparty.factories.bo_user import BOUserPlainFactory
from hatterparty.models import BOUser


@pytest.fixture
def bo_user(db):
    bo_user = BOUserPlainFactory(email="<EMAIL>")
    bo_user.set_password("test")
    bo_user.save(update_fields=["password_hash"])
    return bo_user


@pytest.mark.django_db
def test_bo_user_auth_client_login(bo_user):
    client = APIClient()

    # 403 pre login
    response = client.get(reverse("bo_users-detail", kwargs={"pk": "self"}))
    assert response.status_code == 403

    # login
    bo_user = BOUser.objects.get(email="<EMAIL>")
    client.force_authenticate(user=bo_user)

    # 200 post login
    response = client.get(reverse("bo_users-detail", kwargs={"pk": "self"}))
    assert response.status_code == 200
