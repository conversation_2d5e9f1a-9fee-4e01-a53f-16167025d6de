from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal

from common.db import BaseModel, TemporalValidityMixin, ActivationMixin
from .category import Category


class Product(BaseModel, TemporalValidityMixin, ActivationMixin):
    """
    Product model for items that can be rented for events.
    """
    name = models.CharField(
        verbose_name="Name",
        max_length=200,
        help_text="Product name"
    )
    description = models.TextField(
        verbose_name="Description",
        blank=True,
        help_text="Detailed product description"
    )
    short_description = models.CharField(
        verbose_name="Short description",
        max_length=500,
        blank=True,
        help_text="Brief product description for listings"
    )
    price = models.DecimalField(
        verbose_name="Price",
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Rental price per unit"
    )
    quantity_available = models.PositiveIntegerField(
        verbose_name="Quantity available",
        default=1,
        help_text="Number of units available for rent"
    )
    category = models.ForeignKey(
        Category,
        verbose_name="Category",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="products",
        help_text="Product category"
    )
    image = models.ImageField(
        verbose_name="Image",
        upload_to="products/",
        blank=True,
        null=True,
        help_text="Product image"
    )
    sku = models.CharField(
        verbose_name="SKU",
        max_length=50,
        unique=True,
        blank=True,
        null=True,
        help_text="Stock Keeping Unit identifier"
    )
    weight = models.DecimalField(
        verbose_name="Weight (kg)",
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Product weight in kilograms"
    )
    dimensions = models.CharField(
        verbose_name="Dimensions",
        max_length=100,
        blank=True,
        help_text="Product dimensions (e.g., 100x50x30 cm)"
    )

    class Meta:
        verbose_name = "Product"
        verbose_name_plural = "Products"
        ordering = ["name"]

    def __str__(self):
        return self.name

    @property
    def is_available(self):
        """Check if product is available (active and currently valid)."""
        return self.is_active and self.is_currently_valid

    @property
    def availability_status(self):
        """Get human-readable availability status."""
        if not self.is_active:
            return "Inactive"
        if not self.is_currently_valid:
            return "Not valid"
        if self.quantity_available <= 0:
            return "Out of stock"
        return f"{self.quantity_available} available"
