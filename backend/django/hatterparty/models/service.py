from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal

from common.db import BaseModel, TemporalValidityMixin, ActivationMixin
from .category import Category


class Service(BaseModel, TemporalValidityMixin, ActivationMixin):
    """
    Service model for services that can be booked for events.
    """
    name = models.CharField(
        verbose_name="Name",
        max_length=200,
        help_text="Service name"
    )
    description = models.TextField(
        verbose_name="Description",
        blank=True,
        help_text="Detailed service description"
    )
    short_description = models.CharField(
        verbose_name="Short description",
        max_length=500,
        blank=True,
        help_text="Brief service description for listings"
    )
    price = models.DecimalField(
        verbose_name="Price",
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Service price"
    )
    duration_hours = models.PositiveIntegerField(
        verbose_name="Duration (hours)",
        null=True,
        blank=True,
        help_text="Service duration in hours"
    )
    category = models.ForeignKey(
        Category,
        verbose_name="Category",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="services",
        help_text="Service category"
    )
    image = models.ImageField(
        verbose_name="Image",
        upload_to="services/",
        blank=True,
        null=True,
        help_text="Service image"
    )
    max_participants = models.PositiveIntegerField(
        verbose_name="Max participants",
        null=True,
        blank=True,
        help_text="Maximum number of participants for this service"
    )
    requires_equipment = models.BooleanField(
        verbose_name="Requires equipment",
        default=False,
        help_text="Whether this service requires special equipment"
    )

    class Meta:
        verbose_name = "Service"
        verbose_name_plural = "Services"
        ordering = ["name"]

    def __str__(self):
        return self.name

    @property
    def is_available(self):
        """Check if service is available (active and currently valid)."""
        return self.is_active and self.is_currently_valid

    @property
    def availability_status(self):
        """Get human-readable availability status."""
        if not self.is_active:
            return "Inactive"
        if not self.is_currently_valid:
            return "Not valid"
        return "Available"
