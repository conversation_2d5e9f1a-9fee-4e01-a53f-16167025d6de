from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal

from common.db import BaseModel, TemporalValidityMixin, ActivationMixin
from .category import Category


class Location(BaseModel, TemporalValidityMixin, ActivationMixin):
    """
    Location model for venues that can be rented for events.
    """
    name = models.CharField(
        verbose_name="Name",
        max_length=200,
        help_text="Location name"
    )
    description = models.TextField(
        verbose_name="Description",
        blank=True,
        help_text="Detailed location description"
    )
    short_description = models.CharField(
        verbose_name="Short description",
        max_length=500,
        blank=True,
        help_text="Brief location description for listings"
    )
    price = models.DecimalField(
        verbose_name="Price per day",
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Rental price per day"
    )
    address = models.TextField(
        verbose_name="Address",
        help_text="Full address of the location"
    )
    city = models.Char<PERSON><PERSON>(
        verbose_name="City",
        max_length=100,
        help_text="City where the location is situated"
    )
    postal_code = models.CharField(
        verbose_name="Postal code",
        max_length=20,
        help_text="Postal code"
    )
    capacity = models.PositiveIntegerField(
        verbose_name="Capacity",
        help_text="Maximum number of people the location can accommodate"
    )
    category = models.ForeignKey(
        Category,
        verbose_name="Category",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="locations",
        help_text="Location category"
    )
    image = models.ImageField(
        verbose_name="Image",
        upload_to="locations/",
        blank=True,
        null=True,
        help_text="Location image"
    )
    has_parking = models.BooleanField(
        verbose_name="Has parking",
        default=False,
        help_text="Whether the location has parking available"
    )
    has_catering = models.BooleanField(
        verbose_name="Has catering",
        default=False,
        help_text="Whether the location provides catering services"
    )
    indoor = models.BooleanField(
        verbose_name="Indoor",
        default=True,
        help_text="Whether the location is indoor"
    )
    outdoor = models.BooleanField(
        verbose_name="Outdoor",
        default=False,
        help_text="Whether the location has outdoor space"
    )

    class Meta:
        verbose_name = "Location"
        verbose_name_plural = "Locations"
        ordering = ["name"]

    def __str__(self):
        return f"{self.name} - {self.city}"

    @property
    def is_available(self):
        """Check if location is available (active and currently valid)."""
        return self.is_active and self.is_currently_valid

    @property
    def availability_status(self):
        """Get human-readable availability status."""
        if not self.is_active:
            return "Inactive"
        if not self.is_currently_valid:
            return "Not valid"
        return "Available"

    @property
    def full_address(self):
        """Get formatted full address."""
        return f"{self.address}, {self.city} {self.postal_code}"
