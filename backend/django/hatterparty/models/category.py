from django.db import models

from common.db import BaseModel


class Category(BaseModel):
    """
    Category model for organizing products, services, and locations.
    """
    name = models.CharField(
        verbose_name="Name",
        max_length=100,
        unique=True,
        help_text="Category name"
    )
    description = models.TextField(
        verbose_name="Description",
        blank=True,
        help_text="Category description"
    )
    slug = models.SlugField(
        verbose_name="Slug",
        max_length=100,
        unique=True,
        help_text="URL-friendly category identifier"
    )

    class Meta:
        verbose_name = "Category"
        verbose_name_plural = "Categories"
        ordering = ["name"]

    def __str__(self):
        return self.name
