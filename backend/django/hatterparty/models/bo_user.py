from django.db import models

from common.db import BaseUserModel


class BOUser(BaseUserModel):
    first_name = models.CharField(verbose_name="Nome", blank=True)
    last_name = models.CharField(verbose_name="Cognome", blank=True)

    class Meta:
        verbose_name = "Utente di Backoffice"
        verbose_name_plural = "Utenti di Backoffice"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
