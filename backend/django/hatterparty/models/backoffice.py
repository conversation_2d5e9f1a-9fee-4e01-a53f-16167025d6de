from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal

from common.db import BaseModel, TemporalValidityMixin, ActivationMixin


class Category(BaseModel):
    """
    Category model for organizing products, services, and locations.
    """
    name = models.Char<PERSON>ield(
        verbose_name="Name",
        max_length=100,
        unique=True,
        help_text="Category name"
    )
    description = models.TextField(
        verbose_name="Description",
        blank=True,
        help_text="Category description"
    )
    slug = models.SlugField(
        verbose_name="Slug",
        max_length=100,
        unique=True,
        help_text="URL-friendly category identifier"
    )

    class Meta:
        verbose_name = "Category"
        verbose_name_plural = "Categories"
        ordering = ["name"]

    def __str__(self):
        return self.name


class Product(BaseModel, TemporalValidityMixin, ActivationMixin):
    """
    Product model for items that can be rented for events.
    """
    name = models.CharField(
        verbose_name="Name",
        max_length=200,
        help_text="Product name"
    )
    description = models.TextField(
        verbose_name="Description",
        blank=True,
        help_text="Detailed product description"
    )
    short_description = models.CharField(
        verbose_name="Short description",
        max_length=500,
        blank=True,
        help_text="Brief product description for listings"
    )
    price = models.DecimalField(
        verbose_name="Price",
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Rental price per unit"
    )
    quantity_available = models.PositiveIntegerField(
        verbose_name="Quantity available",
        default=1,
        help_text="Number of units available for rent"
    )
    category = models.ForeignKey(
        Category,
        verbose_name="Category",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="products",
        help_text="Product category"
    )
    image = models.ImageField(
        verbose_name="Image",
        upload_to="products/",
        blank=True,
        null=True,
        help_text="Product image"
    )
    sku = models.CharField(
        verbose_name="SKU",
        max_length=50,
        unique=True,
        blank=True,
        null=True,
        help_text="Stock Keeping Unit identifier"
    )
    weight = models.DecimalField(
        verbose_name="Weight (kg)",
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Product weight in kilograms"
    )
    dimensions = models.CharField(
        verbose_name="Dimensions",
        max_length=100,
        blank=True,
        help_text="Product dimensions (e.g., 100x50x30 cm)"
    )

    class Meta:
        verbose_name = "Product"
        verbose_name_plural = "Products"
        ordering = ["name"]

    def __str__(self):
        return self.name

    @property
    def is_available(self):
        """Check if product is available (active and currently valid)."""
        return self.is_active and self.is_currently_valid

    @property
    def availability_status(self):
        """Get human-readable availability status."""
        if not self.is_active:
            return "Inactive"
        if not self.is_currently_valid:
            return "Not valid"
        if self.quantity_available <= 0:
            return "Out of stock"
        return f"{self.quantity_available} available"


class Service(BaseModel, TemporalValidityMixin, ActivationMixin):
    """
    Service model for services that can be booked for events.
    """
    name = models.CharField(
        verbose_name="Name",
        max_length=200,
        help_text="Service name"
    )
    description = models.TextField(
        verbose_name="Description",
        blank=True,
        help_text="Detailed service description"
    )
    short_description = models.CharField(
        verbose_name="Short description",
        max_length=500,
        blank=True,
        help_text="Brief service description for listings"
    )
    price = models.DecimalField(
        verbose_name="Price",
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Service price"
    )
    duration_hours = models.PositiveIntegerField(
        verbose_name="Duration (hours)",
        null=True,
        blank=True,
        help_text="Service duration in hours"
    )
    category = models.ForeignKey(
        Category,
        verbose_name="Category",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="services",
        help_text="Service category"
    )
    image = models.ImageField(
        verbose_name="Image",
        upload_to="services/",
        blank=True,
        null=True,
        help_text="Service image"
    )
    max_participants = models.PositiveIntegerField(
        verbose_name="Max participants",
        null=True,
        blank=True,
        help_text="Maximum number of participants for this service"
    )
    requires_equipment = models.BooleanField(
        verbose_name="Requires equipment",
        default=False,
        help_text="Whether this service requires special equipment"
    )

    class Meta:
        verbose_name = "Service"
        verbose_name_plural = "Services"
        ordering = ["name"]

    def __str__(self):
        return self.name

    @property
    def is_available(self):
        """Check if service is available (active and currently valid)."""
        return self.is_active and self.is_currently_valid

    @property
    def availability_status(self):
        """Get human-readable availability status."""
        if not self.is_active:
            return "Inactive"
        if not self.is_currently_valid:
            return "Not valid"
        return "Available"


class Location(BaseModel, TemporalValidityMixin, ActivationMixin):
    """
    Location model for venues that can be rented for events.
    """
    name = models.CharField(
        verbose_name="Name",
        max_length=200,
        help_text="Location name"
    )
    description = models.TextField(
        verbose_name="Description",
        blank=True,
        help_text="Detailed location description"
    )
    short_description = models.CharField(
        verbose_name="Short description",
        max_length=500,
        blank=True,
        help_text="Brief location description for listings"
    )
    price = models.DecimalField(
        verbose_name="Price per day",
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Rental price per day"
    )
    address = models.TextField(
        verbose_name="Address",
        help_text="Full address of the location"
    )
    city = models.CharField(
        verbose_name="City",
        max_length=100,
        help_text="City where the location is situated"
    )
    postal_code = models.CharField(
        verbose_name="Postal code",
        max_length=20,
        help_text="Postal code"
    )
    capacity = models.PositiveIntegerField(
        verbose_name="Capacity",
        help_text="Maximum number of people the location can accommodate"
    )
    category = models.ForeignKey(
        Category,
        verbose_name="Category",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="locations",
        help_text="Location category"
    )
    image = models.ImageField(
        verbose_name="Image",
        upload_to="locations/",
        blank=True,
        null=True,
        help_text="Location image"
    )
    has_parking = models.BooleanField(
        verbose_name="Has parking",
        default=False,
        help_text="Whether the location has parking available"
    )
    has_catering = models.BooleanField(
        verbose_name="Has catering",
        default=False,
        help_text="Whether the location provides catering services"
    )
    indoor = models.BooleanField(
        verbose_name="Indoor",
        default=True,
        help_text="Whether the location is indoor"
    )
    outdoor = models.BooleanField(
        verbose_name="Outdoor",
        default=False,
        help_text="Whether the location has outdoor space"
    )

    class Meta:
        verbose_name = "Location"
        verbose_name_plural = "Locations"
        ordering = ["name"]

    def __str__(self):
        return f"{self.name} - {self.city}"

    @property
    def is_available(self):
        """Check if location is available (active and currently valid)."""
        return self.is_active and self.is_currently_valid

    @property
    def availability_status(self):
        """Get human-readable availability status."""
        if not self.is_active:
            return "Inactive"
        if not self.is_currently_valid:
            return "Not valid"
        return "Available"

    @property
    def full_address(self):
        """Get formatted full address."""
        return f"{self.address}, {self.city} {self.postal_code}"
