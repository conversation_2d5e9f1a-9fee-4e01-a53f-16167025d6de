import factory
from django.contrib.auth.hashers import make_password

from hat<PERSON><PERSON><PERSON>.models import BOUser


class BOUserPlainFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = BOUser


class BOUserFactory(BOUserPlainFactory):
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    password = factory.Faker("password", length=10, special_chars=False)
    password_hash = factory.LazyAttribute(lambda obj: make_password(obj.password))

    class Meta:
        model = BOUser
        exclude = ["password"]
