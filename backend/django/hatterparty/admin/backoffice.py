from django.contrib import admin
from django.utils.html import format_html

from common.admin import PrettyJSONMixin
from hatterparty.models import Category, Product, Service, Location


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    """Admin configuration for Category model."""
    
    list_display = [
        "name",
        "slug", 
        "description_preview",
        "product_count",
        "service_count",
        "location_count",
        "created_at",
    ]
    list_filter = ["created_at"]
    search_fields = ["name", "description", "slug"]
    prepopulated_fields = {"slug": ("name",)}
    readonly_fields = ["created_at", "modified_at"]
    date_hierarchy = "created_at"
    
    fieldsets = (
        (None, {
            "fields": ("name", "slug", "description")
        }),
        ("Timestamps", {
            "fields": ("created_at", "modified_at"),
            "classes": ("collapse",)
        }),
    )

    def description_preview(self, obj):
        """Show truncated description."""
        if obj.description:
            return obj.description[:50] + "..." if len(obj.description) > 50 else obj.description
        return "-"
    description_preview.short_description = "Description"

    def product_count(self, obj):
        """Show number of products in this category."""
        return obj.products.count()
    product_count.short_description = "Products"

    def service_count(self, obj):
        """Show number of services in this category."""
        return obj.services.count()
    service_count.short_description = "Services"

    def location_count(self, obj):
        """Show number of locations in this category."""
        return obj.locations.count()
    location_count.short_description = "Locations"


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    """Admin configuration for Product model."""
    
    list_display = [
        "name",
        "category",
        "price",
        "quantity_available",
        "availability_status_display",
        "is_active",
        "valid_from",
        "valid_until",
        "created_at",
    ]
    list_filter = [
        "is_active",
        "category",
        "created_at",
        "valid_from",
        "valid_until",
    ]
    search_fields = ["name", "description", "short_description", "sku"]
    readonly_fields = [
        "created_at", 
        "modified_at", 
        "availability_status",
        "is_available",
        "is_currently_valid",
        "activation_status",
    ]
    date_hierarchy = "created_at"
    list_per_page = 25
    
    fieldsets = (
        (None, {
            "fields": ("name", "category", "sku")
        }),
        ("Description", {
            "fields": ("short_description", "description")
        }),
        ("Pricing & Availability", {
            "fields": ("price", "quantity_available")
        }),
        ("Physical Properties", {
            "fields": ("weight", "dimensions", "image"),
            "classes": ("collapse",)
        }),
        ("Temporal Validity", {
            "fields": ("valid_from", "valid_until", "is_currently_valid"),
            "classes": ("collapse",)
        }),
        ("Activation", {
            "fields": ("is_active", "activated_at", "deactivated_at", "activation_status"),
            "classes": ("collapse",)
        }),
        ("Status", {
            "fields": ("availability_status", "is_available"),
            "classes": ("collapse",)
        }),
        ("Timestamps", {
            "fields": ("created_at", "modified_at"),
            "classes": ("collapse",)
        }),
    )

    def availability_status_display(self, obj):
        """Display availability status with color coding."""
        status = obj.availability_status
        if "available" in status.lower():
            color = "green"
        elif "inactive" in status.lower() or "not valid" in status.lower():
            color = "red"
        elif "out of stock" in status.lower():
            color = "orange"
        else:
            color = "gray"
        
        return format_html(
            '<span style="color: {};">{}</span>',
            color,
            status
        )
    availability_status_display.short_description = "Status"

    actions = ["activate_products", "deactivate_products"]

    def activate_products(self, request, queryset):
        """Bulk activate products."""
        for product in queryset:
            product.activate()
            product.save()
        self.message_user(request, f"Activated {queryset.count()} products.")
    activate_products.short_description = "Activate selected products"

    def deactivate_products(self, request, queryset):
        """Bulk deactivate products."""
        for product in queryset:
            product.deactivate()
            product.save()
        self.message_user(request, f"Deactivated {queryset.count()} products.")
    deactivate_products.short_description = "Deactivate selected products"


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    """Admin configuration for Service model."""
    
    list_display = [
        "name",
        "category",
        "price",
        "duration_hours",
        "max_participants",
        "availability_status_display",
        "is_active",
        "valid_from",
        "valid_until",
        "created_at",
    ]
    list_filter = [
        "is_active",
        "category",
        "requires_equipment",
        "created_at",
        "valid_from",
        "valid_until",
    ]
    search_fields = ["name", "description", "short_description"]
    readonly_fields = [
        "created_at", 
        "modified_at", 
        "availability_status",
        "is_available",
        "is_currently_valid",
        "activation_status",
    ]
    date_hierarchy = "created_at"
    list_per_page = 25
    
    fieldsets = (
        (None, {
            "fields": ("name", "category")
        }),
        ("Description", {
            "fields": ("short_description", "description")
        }),
        ("Service Details", {
            "fields": ("price", "duration_hours", "max_participants", "requires_equipment")
        }),
        ("Media", {
            "fields": ("image",),
            "classes": ("collapse",)
        }),
        ("Temporal Validity", {
            "fields": ("valid_from", "valid_until", "is_currently_valid"),
            "classes": ("collapse",)
        }),
        ("Activation", {
            "fields": ("is_active", "activated_at", "deactivated_at", "activation_status"),
            "classes": ("collapse",)
        }),
        ("Status", {
            "fields": ("availability_status", "is_available"),
            "classes": ("collapse",)
        }),
        ("Timestamps", {
            "fields": ("created_at", "modified_at"),
            "classes": ("collapse",)
        }),
    )

    def availability_status_display(self, obj):
        """Display availability status with color coding."""
        status = obj.availability_status
        if status == "Available":
            color = "green"
        elif "Inactive" in status or "Not valid" in status:
            color = "red"
        else:
            color = "gray"
        
        return format_html(
            '<span style="color: {};">{}</span>',
            color,
            status
        )
    availability_status_display.short_description = "Status"

    actions = ["activate_services", "deactivate_services"]

    def activate_services(self, request, queryset):
        """Bulk activate services."""
        for service in queryset:
            service.activate()
            service.save()
        self.message_user(request, f"Activated {queryset.count()} services.")
    activate_services.short_description = "Activate selected services"

    def deactivate_services(self, request, queryset):
        """Bulk deactivate services."""
        for service in queryset:
            service.deactivate()
            service.save()
        self.message_user(request, f"Deactivated {queryset.count()} services.")
    deactivate_services.short_description = "Deactivate selected services"


@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    """Admin configuration for Location model."""
    
    list_display = [
        "name",
        "city",
        "category",
        "price",
        "capacity",
        "availability_status_display",
        "is_active",
        "valid_from",
        "valid_until",
        "created_at",
    ]
    list_filter = [
        "is_active",
        "category",
        "city",
        "has_parking",
        "has_catering",
        "indoor",
        "outdoor",
        "created_at",
        "valid_from",
        "valid_until",
    ]
    search_fields = ["name", "description", "short_description", "address", "city"]
    readonly_fields = [
        "created_at", 
        "modified_at", 
        "availability_status",
        "is_available",
        "is_currently_valid",
        "activation_status",
        "full_address",
    ]
    date_hierarchy = "created_at"
    list_per_page = 25
    
    fieldsets = (
        (None, {
            "fields": ("name", "category")
        }),
        ("Description", {
            "fields": ("short_description", "description")
        }),
        ("Location Details", {
            "fields": ("address", "city", "postal_code", "full_address")
        }),
        ("Capacity & Pricing", {
            "fields": ("capacity", "price")
        }),
        ("Amenities", {
            "fields": ("has_parking", "has_catering", "indoor", "outdoor"),
            "classes": ("collapse",)
        }),
        ("Media", {
            "fields": ("image",),
            "classes": ("collapse",)
        }),
        ("Temporal Validity", {
            "fields": ("valid_from", "valid_until", "is_currently_valid"),
            "classes": ("collapse",)
        }),
        ("Activation", {
            "fields": ("is_active", "activated_at", "deactivated_at", "activation_status"),
            "classes": ("collapse",)
        }),
        ("Status", {
            "fields": ("availability_status", "is_available"),
            "classes": ("collapse",)
        }),
        ("Timestamps", {
            "fields": ("created_at", "modified_at"),
            "classes": ("collapse",)
        }),
    )

    def availability_status_display(self, obj):
        """Display availability status with color coding."""
        status = obj.availability_status
        if status == "Available":
            color = "green"
        elif "Inactive" in status or "Not valid" in status:
            color = "red"
        else:
            color = "gray"
        
        return format_html(
            '<span style="color: {};">{}</span>',
            color,
            status
        )
    availability_status_display.short_description = "Status"

    actions = ["activate_locations", "deactivate_locations"]

    def activate_locations(self, request, queryset):
        """Bulk activate locations."""
        for location in queryset:
            location.activate()
            location.save()
        self.message_user(request, f"Activated {queryset.count()} locations.")
    activate_locations.short_description = "Activate selected locations"

    def deactivate_locations(self, request, queryset):
        """Bulk deactivate locations."""
        for location in queryset:
            location.deactivate()
            location.save()
        self.message_user(request, f"Deactivated {queryset.count()} locations.")
    deactivate_locations.short_description = "Deactivate selected locations"
