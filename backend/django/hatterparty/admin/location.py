from django.contrib import admin
from django.utils.html import format_html

from hatterparty.models import Location


@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    """Admin configuration for Location model."""
    
    list_display = [
        "name",
        "city",
        "category",
        "price",
        "capacity",
        "availability_status_display",
        "is_active",
        "valid_from",
        "valid_until",
        "created_at",
    ]
    list_filter = [
        "is_active",
        "category",
        "city",
        "has_parking",
        "has_catering",
        "indoor",
        "outdoor",
        "created_at",
        "valid_from",
        "valid_until",
    ]
    search_fields = ["name", "description", "short_description", "address", "city"]
    readonly_fields = [
        "created_at", 
        "modified_at", 
        "availability_status",
        "is_available",
        "is_currently_valid",
        "activation_status",
        "full_address",
    ]
    date_hierarchy = "created_at"
    list_per_page = 25
    
    fieldsets = (
        (None, {
            "fields": ("name", "category")
        }),
        ("Description", {
            "fields": ("short_description", "description")
        }),
        ("Location Details", {
            "fields": ("address", "city", "postal_code", "full_address")
        }),
        ("Capacity & Pricing", {
            "fields": ("capacity", "price")
        }),
        ("Amenities", {
            "fields": ("has_parking", "has_catering", "indoor", "outdoor"),
            "classes": ("collapse",)
        }),
        ("Media", {
            "fields": ("image",),
            "classes": ("collapse",)
        }),
        ("Temporal Validity", {
            "fields": ("valid_from", "valid_until", "is_currently_valid"),
            "classes": ("collapse",)
        }),
        ("Activation", {
            "fields": ("is_active", "activated_at", "deactivated_at", "activation_status"),
            "classes": ("collapse",)
        }),
        ("Status", {
            "fields": ("availability_status", "is_available"),
            "classes": ("collapse",)
        }),
        ("Timestamps", {
            "fields": ("created_at", "modified_at"),
            "classes": ("collapse",)
        }),
    )

    def availability_status_display(self, obj):
        """Display availability status with color coding."""
        status = obj.availability_status
        if status == "Available":
            color = "green"
        elif "Inactive" in status or "Not valid" in status:
            color = "red"
        else:
            color = "gray"
        
        return format_html(
            '<span style="color: {};">{}</span>',
            color,
            status
        )
    availability_status_display.short_description = "Status"

    actions = ["activate_locations", "deactivate_locations"]

    def activate_locations(self, request, queryset):
        """Bulk activate locations."""
        for location in queryset:
            location.activate()
            location.save()
        self.message_user(request, f"Activated {queryset.count()} locations.")
    activate_locations.short_description = "Activate selected locations"

    def deactivate_locations(self, request, queryset):
        """Bulk deactivate locations."""
        for location in queryset:
            location.deactivate()
            location.save()
        self.message_user(request, f"Deactivated {queryset.count()} locations.")
    deactivate_locations.short_description = "Deactivate selected locations"
