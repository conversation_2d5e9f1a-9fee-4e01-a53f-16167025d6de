from django.contrib import admin
from django.utils.html import format_html

from hatterparty.models import Product


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    """Admin configuration for Product model."""
    
    list_display = [
        "name",
        "category",
        "price",
        "quantity_available",
        "availability_status_display",
        "is_active",
        "valid_from",
        "valid_until",
        "created_at",
    ]
    list_filter = [
        "is_active",
        "category",
        "created_at",
        "valid_from",
        "valid_until",
    ]
    search_fields = ["name", "description", "short_description", "sku"]
    readonly_fields = [
        "created_at", 
        "modified_at", 
        "availability_status",
        "is_available",
        "is_currently_valid",
        "activation_status",
    ]
    date_hierarchy = "created_at"
    list_per_page = 25
    
    fieldsets = (
        (None, {
            "fields": ("name", "category", "sku")
        }),
        ("Description", {
            "fields": ("short_description", "description")
        }),
        ("Pricing & Availability", {
            "fields": ("price", "quantity_available")
        }),
        ("Physical Properties", {
            "fields": ("weight", "dimensions", "image"),
            "classes": ("collapse",)
        }),
        ("Temporal Validity", {
            "fields": ("valid_from", "valid_until", "is_currently_valid"),
            "classes": ("collapse",)
        }),
        ("Activation", {
            "fields": ("is_active", "activated_at", "deactivated_at", "activation_status"),
            "classes": ("collapse",)
        }),
        ("Status", {
            "fields": ("availability_status", "is_available"),
            "classes": ("collapse",)
        }),
        ("Timestamps", {
            "fields": ("created_at", "modified_at"),
            "classes": ("collapse",)
        }),
    )

    def availability_status_display(self, obj):
        """Display availability status with color coding."""
        status = obj.availability_status
        if "available" in status.lower():
            color = "green"
        elif "inactive" in status.lower() or "not valid" in status.lower():
            color = "red"
        elif "out of stock" in status.lower():
            color = "orange"
        else:
            color = "gray"
        
        return format_html(
            '<span style="color: {};">{}</span>',
            color,
            status
        )
    availability_status_display.short_description = "Status"

    actions = ["activate_products", "deactivate_products"]

    def activate_products(self, request, queryset):
        """Bulk activate products."""
        for product in queryset:
            product.activate()
            product.save()
        self.message_user(request, f"Activated {queryset.count()} products.")
    activate_products.short_description = "Activate selected products"

    def deactivate_products(self, request, queryset):
        """Bulk deactivate products."""
        for product in queryset:
            product.deactivate()
            product.save()
        self.message_user(request, f"Deactivated {queryset.count()} products.")
    deactivate_products.short_description = "Deactivate selected products"
