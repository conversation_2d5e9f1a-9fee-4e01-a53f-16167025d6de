from django.contrib import admin
from django.utils.html import format_html

from hatterparty.models import Service


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    """Admin configuration for Service model."""
    
    list_display = [
        "name",
        "category",
        "price",
        "duration_hours",
        "max_participants",
        "availability_status_display",
        "is_active",
        "valid_from",
        "valid_until",
        "created_at",
    ]
    list_filter = [
        "is_active",
        "category",
        "requires_equipment",
        "created_at",
        "valid_from",
        "valid_until",
    ]
    search_fields = ["name", "description", "short_description"]
    readonly_fields = [
        "created_at", 
        "modified_at", 
        "availability_status",
        "is_available",
        "is_currently_valid",
        "activation_status",
    ]
    date_hierarchy = "created_at"
    list_per_page = 25
    
    fieldsets = (
        (None, {
            "fields": ("name", "category")
        }),
        ("Description", {
            "fields": ("short_description", "description")
        }),
        ("Service Details", {
            "fields": ("price", "duration_hours", "max_participants", "requires_equipment")
        }),
        ("Media", {
            "fields": ("image",),
            "classes": ("collapse",)
        }),
        ("Temporal Validity", {
            "fields": ("valid_from", "valid_until", "is_currently_valid"),
            "classes": ("collapse",)
        }),
        ("Activation", {
            "fields": ("is_active", "activated_at", "deactivated_at", "activation_status"),
            "classes": ("collapse",)
        }),
        ("Status", {
            "fields": ("availability_status", "is_available"),
            "classes": ("collapse",)
        }),
        ("Timestamps", {
            "fields": ("created_at", "modified_at"),
            "classes": ("collapse",)
        }),
    )

    def availability_status_display(self, obj):
        """Display availability status with color coding."""
        status = obj.availability_status
        if status == "Available":
            color = "green"
        elif "Inactive" in status or "Not valid" in status:
            color = "red"
        else:
            color = "gray"
        
        return format_html(
            '<span style="color: {};">{}</span>',
            color,
            status
        )
    availability_status_display.short_description = "Status"

    actions = ["activate_services", "deactivate_services"]

    def activate_services(self, request, queryset):
        """Bulk activate services."""
        for service in queryset:
            service.activate()
            service.save()
        self.message_user(request, f"Activated {queryset.count()} services.")
    activate_services.short_description = "Activate selected services"

    def deactivate_services(self, request, queryset):
        """Bulk deactivate services."""
        for service in queryset:
            service.deactivate()
            service.save()
        self.message_user(request, f"Deactivated {queryset.count()} services.")
    deactivate_services.short_description = "Deactivate selected services"
