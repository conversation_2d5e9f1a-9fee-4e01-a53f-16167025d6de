from django.contrib import admin

from hatterparty.models import Category


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    """Admin configuration for Category model."""
    
    list_display = [
        "name",
        "slug", 
        "description_preview",
        "product_count",
        "service_count",
        "location_count",
        "created_at",
    ]
    list_filter = ["created_at"]
    search_fields = ["name", "description", "slug"]
    prepopulated_fields = {"slug": ("name",)}
    readonly_fields = ["created_at", "modified_at"]
    date_hierarchy = "created_at"
    
    fieldsets = (
        (None, {
            "fields": ("name", "slug", "description")
        }),
        ("Timestamps", {
            "fields": ("created_at", "modified_at"),
            "classes": ("collapse",)
        }),
    )

    def description_preview(self, obj):
        """Show truncated description."""
        if obj.description:
            return obj.description[:50] + "..." if len(obj.description) > 50 else obj.description
        return "-"
    description_preview.short_description = "Description"

    def product_count(self, obj):
        """Show number of products in this category."""
        return obj.products.count()
    product_count.short_description = "Products"

    def service_count(self, obj):
        """Show number of services in this category."""
        return obj.services.count()
    service_count.short_description = "Services"

    def location_count(self, obj):
        """Show number of locations in this category."""
        return obj.locations.count()
    location_count.short_description = "Locations"
