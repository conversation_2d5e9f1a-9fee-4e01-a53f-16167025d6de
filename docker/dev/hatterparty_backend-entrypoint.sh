#!/bin/sh

if [ -z "$(ls -A /opt/venv/)" ]; then
    python -m venv /opt/venv
    source /opt/venv/bin/activate
    pip install --upgrade pip
    pip install --upgrade uv
    uv pip sync requirements.txt dev-requirements.txt
else
    source /opt/venv/bin/activate
fi

>&2 echo "Waiting for database..."
while ! nc -z hatterparty_db 5432; do
    sleep 0.1
done
>&2 echo "...database is running."

exec "$@"