FROM --platform=linux/amd64 python:3.12-alpine3.20

# Initial setup
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV PYTHONBREAKPOINT ipdb.set_trace
ENV COSTING_ENV dev

# Install dependencies
RUN apk update
RUN apk add --no-cache git openssh-client

# Configure SSH for git
RUN mkdir -p -m 0700 ~/.ssh && ssh-keyscan github.com >> ~/.ssh/known_hosts
RUN git config --global --add safe.directory /usr/src/hatterparty

WORKDIR /usr/src/hatterparty/backend/django

RUN python -m ensurepip --upgrade

ENTRYPOINT ["/usr/src/hatterparty/docker/dev/hatterparty_backend-entrypoint.sh"]