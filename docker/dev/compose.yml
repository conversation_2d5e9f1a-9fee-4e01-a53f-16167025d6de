services:
  hatterparty_db:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: hatterparty
      POSTGRES_PASSWORD: hatterparty
      POSTGRES_DB: hatterparty
    expose:
      - 5432
    volumes:
      - ../shared:/shared
      - hatterparty_postgres_data:/var/lib/postgresql/data/

  hatterparty_backend:
    container_name: hatterparty_backend
    build:
      context: ../..
      dockerfile: docker/dev/hatterparty_backend.Dockerfile
    image: hatterparty_backend
    stdin_open: true
    tty: true
    volumes:
      - ../..:/usr/src/hatterparty/
      - ipython_history:/root/.ipython
      - hatterparty_media:/usr/src/hatterparty/backend/django/project/media/
      - hatterparty_venv:/opt/venv
    ports:
      - 3001:3000
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      - hatterparty_db
    command: >
      sh -c 'python -Wa manage.py runserver 0.0.0.0:8000;'

  hatterparty_frontend:
    build:
      context: ../..
      dockerfile: docker/dev/hatterparty_frontend.Dockerfile
    image: hatterparty_frontend
    volumes:
      - ../..:/usr/src/hatterparty/
      - ../../frontend/node_modules:/usr/src/hatterparty/frontend/node_modules
    entrypoint: /usr/src/hatterparty/docker/dev/hatterparty_frontend-entrypoint.sh

  hatterparty_backoffice:
    image: hatterparty_frontend
    expose:
      - 5173
    volumes:
      - ../..:/usr/src/hatterparty/
      - ../../frontend/node_modules:/usr/src/hatterparty/frontend/node_modules
    depends_on:
      hatterparty_frontend:
        condition: service_completed_successfully
    command: npm run backoffice:dev

  hatterparty_ws:
    build:
      context: ../..
      dockerfile: docker/dev/hatterparty_ws.Dockerfile
    ports:
      - 80:8080
    depends_on:
      - hatterparty_backend
      - hatterparty_backoffice

volumes:
  ipython_history:
  hatterparty_media:
  hatterparty_postgres_data:
  hatterparty_venv:
