upstream hatterparty_backend {
    server hatterparty_backend:8000;
}

server {
    listen 8080;
    server_name backend.hatterparty.localhost;
    client_max_body_size 20M;

    location / {
        proxy_pass http://hatterparty_backend;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        proxy_redirect off;
    }
}

upstream hatterparty_backoffice {
    server hatterparty_backoffice:5173;
}

server {
    listen 8080;
    server_name backoffice.hatterparty.localhost;

    location / {
        proxy_pass http://hatterparty_backoffice;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
        proxy_redirect off;
    }
}
