#!/usr/bin/env -S bash -e

# How to lint this script:
## sudo apt install shellcheck
## shellcheck -x dev-tools.sh

SCRIPT_NAME="dev-tools"

if [[ ( "$(basename -- "$0")" != "$SCRIPT_NAME.sh" ) || ( $ZSH_EVAL_CONTEXT =~ ':file' ) ]]; then
    SOURCED=  # Called with source or .
fi

. ./_tools-include.sh


run_backend_tests() {
    local pytest_args="$*"
    export COSTING_ENV=test
    docker compose run --rm hatterparty_backend sh -c "pytest $pytest_args"
}


AVAILABLE_COMMANDS=(
    "backend-shell       Run sh in backend container"
    "debug-runserver     Attach terminal to running runserver"
    "docker-compose      Run docker compose command"
    "docker-build        Build all containers"
    "docker-up           Start all containers"
    "docker-down         Stops all containers"
    "docker-clean        Remove all unused Docker containers, networks, images, volumes"
    "frontend-shell      Run sh in frontend container"
    "frontend-shell      Run sh in frontend container"
    "format-backend      Format the backend codebase"
    "format-frontend     Format the frontend codebase"
    "format-frontend-fix Try to fix frontend formatting errors"
    "lint                Lint the codebase"
    "lint-backend        Lint the backend codebase"
    "lint-frontend       Lint the frontend codebase"
    "lint-frontend-fix   Try to fix frontend linting errors"
    "manage              Run manage.py django script"
    "npm                 Run npm in frontend container"
    "npx                 Run npx in frontend container"
    "pip-compile         Compile pip requirements"
    "pip-outdated        Show outdated pip packages"
    "pip-sync            Sync pip requirements"
    "psql                Run psql connected to local database"
    "ruff-fix            Try to fix backend linting errors"
    "test-backend        Run backend testsuite"
)

check_docker () {
    which docker >/dev/null || {
        echo "Please install Docker Engine (https://docs.docker.com/engine/install/)"
        exit 1
    }
}

if [[ -v SOURCED ]]; then
    install_autocompletion
    unset AVAILABLE_COMMANDS SCRIPT_NAME SOURCED
    return
fi

REPO="$PWD"
NORMAL_USER="$(logname)"

cd "$REPO/docker/dev"

case $1 in

    backend-shell)
        check_docker
        docker compose run --rm hatterparty_backend sh
        ;;

    debug-runserver)
        check_docker
        echo "Press CTRL+P CTRL+Q to detach from container, leaving runserver running."
        docker attach hatterparty_backend
        ;;

    docker-build)
        check_docker
        docker compose build --no-cache
        ;;

    docker-clean)
        check_docker
        docker compose down -v --remove-orphans --rmi all
        docker system prune --all --volumes --force
        ;;

    docker-compose)
        check_docker
        shift
        docker compose "$@"
        ;;

    docker-up)
        check_docker
        docker compose up --remove-orphans
        ;;

    docker-down)
        check_docker
        docker compose down
        ;;

    format)
        cd "$REPO"
        "$0" format-backend && "$0" format-frontend
        ;;

    format-backend)
        check_docker
        docker compose run --rm hatterparty_backend sh -c "ruff format ."
        ;;

    format-frontend)
        check_docker
        docker compose run --rm hatterparty_frontend sh -c "npm run format"
        ;;

    format-frontend-fix)
        check_docker
        docker compose run --rm hatterparty_frontend sh -c "npm run format:fix"
        ;;

    frontend-shell)
        check_docker
        docker compose run --rm hatterparty_frontend sh
        ;;

    lint)
        cd "$REPO"
        "$0" lint-backend && "$0" lint-frontend
        ;;

    lint-backend)
        check_docker
        docker compose run --rm -T hatterparty_backend sh -c "/usr/src/hatterparty/backend/lint.sh"
        ;;

    lint-frontend)
        check_docker
        docker compose run --rm -T hatterparty_frontend sh -c "/usr/src/hatterparty/frontend/lint.sh"
        ;;

    lint-frontend-fix)
        check_docker
        docker compose run --rm -T hatterparty_frontend sh -c "FIX=1 /usr/src/hatterparty/frontend/lint.sh"
        ;;

    manage)
        check_docker
        shift
        docker compose run --rm hatterparty_backend python -Wa manage.py "$@"
        ;;

    npm)
        check_docker
        shift
        docker compose run --rm hatterparty_frontend npm "$@"
        ;;

    npx)
        check_docker
        shift
        docker compose run --rm hatterparty_frontend npx "$@"
        ;;

    pip-compile)
        check_docker
        if [[ "$2" == "--help" ]]; then
            echo "To add a package:"
            echo "    add the package to requirements.txt or dev-requirements.txt"
            echo "    $0 pip-compile"
            echo "    $0 pip-sync"
            echo "To upgrade a package (e.g. django):"
            echo "    $0 pip-compile django"
            echo "    $0 pip-sync"
            echo "To also update the local virtual environment:"
            echo "    $0 pip-compile --with-local"
            echo "    $0 pip-compile django --with-local"
            exit 0
        fi

        # Check for --with-local parameter
        WITH_LOCAL=false
        PACKAGE=""
        for arg in "$@"; do
            if [[ "$arg" == "--with-local" ]]; then
                WITH_LOCAL=true
            elif [[ "$arg" != "pip-compile" ]]; then
                # Store the package name to be upgraded with pip-compile
                PACKAGE="$arg"
            fi
        done

        if [[ "$PACKAGE" != "" && "$PACKAGE" != "--with-local" ]]; then
            PIP_COMPILE_ARGS="--upgrade-package $PACKAGE"
        else
            PIP_COMPILE_ARGS=""
        fi

        echo "Running pip-compile in Docker container..."
        docker compose run --rm hatterparty_backend sh -c "
            pip install -U pip
            pip install -U uv
            uv pip compile requirements.in --unsafe-package setuptools $PIP_COMPILE_ARGS -o requirements.txt
            uv pip compile dev-requirements.in $PIP_COMPILE_ARGS -o dev-requirements.txt
        "
        cd "$REPO/backend/django"
        sudo chown "$NORMAL_USER":"$NORMAL_USER" requirements.txt dev-requirements.txt

        # Run in local virtual environment if --with-local is specified
        if $WITH_LOCAL; then
            echo "Running pip-compile in local virtual environment..."
            cd "$REPO"
            if [ -d "backend/.venv" ]; then
                cd backend/django
                source "../.venv/bin/activate"
                pip install -U pip
                pip install -U uv
                uv pip compile requirements.in --unsafe-package setuptools $PIP_COMPILE_ARGS -o requirements.txt
                uv pip compile dev-requirements.in $PIP_COMPILE_ARGS -o dev-requirements.txt
                deactivate
                echo "Local virtual environment updated successfully."
            else
                echo "Local virtual environment not found at backend/.venv"
            fi
        fi
        ;;

    pip-outdated)
        check_docker
        if [[ "$2" == "--help" ]]; then
            echo "To check outdated packages in Docker container:"
            echo "    $0 pip-outdated"
            echo "To also check outdated packages in local virtual environment:"
            echo "    $0 pip-outdated --with-local"
            exit 0
        fi

        # Check for --with-local parameter
        WITH_LOCAL=false
        for arg in "$@"; do
            if [[ "$arg" == "--with-local" ]]; then
                WITH_LOCAL=true
            fi
        done

        echo "Checking outdated packages in Docker container..."
        docker compose run --rm hatterparty_backend sh -c "
            pip install -U pip
            pip install -U uv
            uv pip list --outdated
        "

        # Run in local virtual environment if --with-local is specified
        if $WITH_LOCAL; then
            echo "Checking outdated packages in local virtual environment..."
            cd "$REPO"
            if [ -d "backend/.venv" ]; then
                source "backend/.venv/bin/activate"
                pip install -U pip
                pip install -U uv
                uv pip list --outdated
                deactivate
                echo "Local virtual environment check completed."
            else
                echo "Local virtual environment not found at backend/.venv"
            fi
        fi
        ;;

    pip-sync)
        check_docker
        if [[ "$2" == "--help" ]]; then
            echo "To sync packages in Docker container:"
            echo "    $0 pip-sync"
            echo "To also sync packages in local virtual environment:"
            echo "    $0 pip-sync --with-local"
            exit 0
        fi

        # Check for --with-local parameter
        WITH_LOCAL=false
        for arg in "$@"; do
            if [[ "$arg" == "--with-local" ]]; then
                WITH_LOCAL=true
            fi
        done

        echo "Running pip-sync in Docker container..."
        docker compose run --rm hatterparty_backend sh -c "
            pip install -U pip
            pip install -U uv
            uv pip sync requirements.txt dev-requirements.txt
        "

        # Run in local virtual environment if --with-local is specified
        if $WITH_LOCAL; then
            echo "Running pip-sync in local virtual environment..."
            cd "$REPO"
            if [ -d "backend/.venv" ]; then
                cd backend/django
                source "../.venv/bin/activate"
                pip install -U pip
                pip install -U uv
                uv pip sync requirements.txt dev-requirements.txt
                deactivate
                echo "Local virtual environment synchronized successfully."
            else
                echo "Local virtual environment not found at backend/.venv"
            fi
        fi
        ;;

    psql)
        check_docker
        docker compose exec hatterparty_db psql -U hatterparty
        ;;

    ruff-fix)
        check_docker
        docker compose run --rm hatterparty_backend sh -c "ruff check --fix ."
        ;;

    test-backend)
        if [[ "$2" == "--help" ]]; then
            echo "To launch the entire suite:"
            echo "    $0 $1"
            echo "To launch specific tests:"
            echo "    $0 $1 tests/folder"
            echo "    $0 $1 tests/folder/file.py"
            echo "    $0 $1 tests/folder/file.py::test_function"
            exit 0
        fi
        check_docker
        run_backend_tests -s "$2"
        ;;
      *)
        print_help
        ;;
esac
