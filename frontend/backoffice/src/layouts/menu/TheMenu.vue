<script setup>
    import {HAvatar, HPopoverMenu} from '@horizon';
    import {useAuthStore} from '@common/stores/auth';

    import HMenuBar from '@horizon/components/layout/HMenuBar.vue';
    import HatterPartyLogo from '@/components/hatterparty/HatterPartyLogo.vue';

    const {isAuthenticated, user} = useAuthStore();

    const menuItems = computed(() =>
        isAuthenticated.value
            ? [
                  {
                      label: 'Dashboard',
                      icon: 'ri:dashboard-fill',
                      route: {name: 'dashboard'},
                  },
                  {
                      label: 'Products',
                      icon: 'ri:box-3-fill',
                      route: {name: 'product-list'},
                  },
                  {
                      label: 'Services',
                      icon: 'ri:service-fill',
                      route: {name: 'service-list'},
                  },
                  {
                      label: 'Locations',
                      icon: 'ri:map-pin-fill',
                      route: {name: 'location-list'},
                  },
                  {
                      label: 'Categories',
                      icon: 'ri:folder-fill',
                      route: {name: 'category-list'},
                  },
                  {
                      label: 'Customers',
                      icon: 'ri:user-2-fill',
                      route: {name: 'customer-list'},
                  },
              ]
            : [],
    );
    const profileItems = computed(() => [
        {
            label: 'Profile',
            icon: 'ri:user-settings-fill',
            route: {name: 'dashboard'}, // TODO: Add profile route
        },
        {
            label: 'Settings',
            icon: 'ri:settings-fill',
            route: {name: 'dashboard'}, // TODO: Add settings route
        },
        {
            label: 'Logout',
            icon: 'ri:logout-box-fill',
            action: 'logout',
        },
    ]);
</script>

<template>
    <HMenuBar :items="menuItems">
        <template #start>
            <router-link :to="{name: 'dashboard'}" class="flex items-center">
                <HatterPartyLogo />
            </router-link>
        </template>
        <template #end>
            <HPopoverMenu v-if="isAuthenticated" :items="profileItems">
                <template #trigger="{toggle}">
                    <HAvatar
                        image-url="https://i.pravatar.cc/300"
                        :first-name="user.first_name"
                        :last-name="user.last_name"
                        @click="toggle" />
                </template>
            </HPopoverMenu>
        </template>
    </HMenuBar>
</template>
