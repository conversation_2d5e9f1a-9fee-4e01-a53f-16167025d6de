<script setup>
    import {HAvatar, HPopoverMenu} from '@horizon';
    import {useAuthStore} from '@common/stores/auth';

    import HMenuBar from '@horizon/components/layout/HMenuBar.vue';
    import HatterPartyLogo from '@/components/hatterparty/HatterPartyLogo.vue';

    const {isAuthenticated, user} = useAuthStore();

    const menuItems = computed(() =>
        isAuthenticated.value
            ? [
                  {
                      label: 'Clienti',
                      icon: 'ri:user-2-fill',
                      route: {name: 'customer-list'},
                  },
              ]
            : [],
    );
    const profileItems = computed(() => [
        {
            label: 'Clienti',
            icon: 'ri:user-2-fill',
            route: {name: 'customer-list'},
        },
    ]);
</script>

<template>
    <HMenuBar :items="menuItems">
        <template #start>
            <router-link :to="{name: 'dashboard'}" class="flex items-center">
                <HatterPartyLogo />
            </router-link>
        </template>
        <template #end>
            <HPopoverMenu v-if="isAuthenticated" :items="profileItems">
                <template #trigger="{toggle}">
                    <HAvatar
                        image-url="https://i.pravatar.cc/300"
                        :first-name="user.first_name"
                        :last-name="user.last_name"
                        @click="toggle" />
                </template>
            </HPopoverMenu>
        </template>
    </HMenuBar>
</template>
