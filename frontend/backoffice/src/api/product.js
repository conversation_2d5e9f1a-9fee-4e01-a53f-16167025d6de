import api from '@/api/index.js';

/**
 * Product API service functions
 */
export const productService = {
    /**
     * Get all products with optional filtering
     */
    async getAll(params = {}) {
        return await api.products.get('', {params});
    },

    /**
     * Get product by ID
     */
    async getById(id) {
        return await api.products.get(`${id}/`);
    },

    /**
     * Create new product
     */
    async create(data) {
        return await api.products.post('', data);
    },

    /**
     * Update product
     */
    async update(id, data) {
        return await api.products.put(`${id}/`, data);
    },

    /**
     * Delete product
     */
    async delete(id) {
        return await api.products.delete(`${id}/`);
    },

    /**
     * Activate product
     */
    async activate(id) {
        return await api.products.post(`${id}/activate/`);
    },

    /**
     * Deactivate product
     */
    async deactivate(id) {
        return await api.products.post(`${id}/deactivate/`);
    },

    /**
     * Bulk activate products
     */
    async bulkActivate(productIds) {
        return await api.products.post('bulk_activate/', {
            product_ids: productIds
        });
    },

    /**
     * Bulk deactivate products
     */
    async bulkDeactivate(productIds) {
        return await api.products.post('bulk_deactivate/', {
            product_ids: productIds
        });
    },

    /**
     * Search products
     */
    async search(query, params = {}) {
        return await this.getAll({
            search: query,
            ...params
        });
    },

    /**
     * Filter products by category
     */
    async getByCategory(categoryId, params = {}) {
        return await this.getAll({
            category: categoryId,
            ...params
        });
    },

    /**
     * Filter products by availability
     */
    async getAvailable(params = {}) {
        return await this.getAll({
            available: true,
            ...params
        });
    },

    /**
     * Filter products by price range
     */
    async getByPriceRange(minPrice, maxPrice, params = {}) {
        return await this.getAll({
            min_price: minPrice,
            max_price: maxPrice,
            ...params
        });
    },
};
