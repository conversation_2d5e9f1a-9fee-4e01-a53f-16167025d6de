import api from '@/api/index.js';

/**
 * Category API service functions
 */
export const categoryService = {
    /**
     * Get all categories
     */
    async getAll(params = {}) {
        return await api.categories.get('', {params});
    },

    /**
     * Get category by ID
     */
    async getById(id) {
        return await api.categories.get(`${id}/`);
    },

    /**
     * Create new category
     */
    async create(data) {
        return await api.categories.post('', data);
    },

    /**
     * Update category
     */
    async update(id, data) {
        return await api.categories.put(`${id}/`, data);
    },

    /**
     * Delete category
     */
    async delete(id) {
        return await api.categories.delete(`${id}/`);
    },

    /**
     * Get categories for dropdown/select options
     */
    async getOptions() {
        const response = await this.getAll();
        return response.results || response;
    },
};
