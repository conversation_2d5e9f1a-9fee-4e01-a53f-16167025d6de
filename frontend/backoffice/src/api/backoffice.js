import api from '@/api/index.js';

/**
 * Category API service functions
 */
export const categoryService = {
    /**
     * Get all categories
     */
    async getAll(params = {}) {
        return await api.categories.get('', {params});
    },

    /**
     * Get category by ID
     */
    async getById(id) {
        return await api.categories.get(`${id}/`);
    },

    /**
     * Create new category
     */
    async create(data) {
        return await api.categories.post('', data);
    },

    /**
     * Update category
     */
    async update(id, data) {
        return await api.categories.put(`${id}/`, data);
    },

    /**
     * Delete category
     */
    async delete(id) {
        return await api.categories.delete(`${id}/`);
    },

    /**
     * Get categories for dropdown/select options
     */
    async getOptions() {
        const response = await this.getAll();
        return response.results || response;
    },
};

/**
 * Product API service functions
 */
export const productService = {
    /**
     * Get all products with optional filtering
     */
    async getAll(params = {}) {
        return await api.products.get('', {params});
    },

    /**
     * Get product by ID
     */
    async getById(id) {
        return await api.products.get(`${id}/`);
    },

    /**
     * Create new product
     */
    async create(data) {
        return await api.products.post('', data);
    },

    /**
     * Update product
     */
    async update(id, data) {
        return await api.products.put(`${id}/`, data);
    },

    /**
     * Delete product
     */
    async delete(id) {
        return await api.products.delete(`${id}/`);
    },

    /**
     * Activate product
     */
    async activate(id) {
        return await api.products.post(`${id}/activate/`);
    },

    /**
     * Deactivate product
     */
    async deactivate(id) {
        return await api.products.post(`${id}/deactivate/`);
    },

    /**
     * Bulk activate products
     */
    async bulkActivate(productIds) {
        return await api.products.post('bulk_activate/', {
            product_ids: productIds
        });
    },

    /**
     * Bulk deactivate products
     */
    async bulkDeactivate(productIds) {
        return await api.products.post('bulk_deactivate/', {
            product_ids: productIds
        });
    },

    /**
     * Search products
     */
    async search(query, params = {}) {
        return await this.getAll({
            search: query,
            ...params
        });
    },

    /**
     * Filter products by category
     */
    async getByCategory(categoryId, params = {}) {
        return await this.getAll({
            category: categoryId,
            ...params
        });
    },

    /**
     * Filter products by availability
     */
    async getAvailable(params = {}) {
        return await this.getAll({
            available: true,
            ...params
        });
    },

    /**
     * Filter products by price range
     */
    async getByPriceRange(minPrice, maxPrice, params = {}) {
        return await this.getAll({
            min_price: minPrice,
            max_price: maxPrice,
            ...params
        });
    },
};

/**
 * Service API service functions
 */
export const serviceService = {
    /**
     * Get all services with optional filtering
     */
    async getAll(params = {}) {
        return await api.services.get('', {params});
    },

    /**
     * Get service by ID
     */
    async getById(id) {
        return await api.services.get(`${id}/`);
    },

    /**
     * Create new service
     */
    async create(data) {
        return await api.services.post('', data);
    },

    /**
     * Update service
     */
    async update(id, data) {
        return await api.services.put(`${id}/`, data);
    },

    /**
     * Delete service
     */
    async delete(id) {
        return await api.services.delete(`${id}/`);
    },

    /**
     * Activate service
     */
    async activate(id) {
        return await api.services.post(`${id}/activate/`);
    },

    /**
     * Deactivate service
     */
    async deactivate(id) {
        return await api.services.post(`${id}/deactivate/`);
    },

    /**
     * Bulk activate services
     */
    async bulkActivate(serviceIds) {
        return await api.services.post('bulk_activate/', {
            service_ids: serviceIds
        });
    },

    /**
     * Bulk deactivate services
     */
    async bulkDeactivate(serviceIds) {
        return await api.services.post('bulk_deactivate/', {
            service_ids: serviceIds
        });
    },

    /**
     * Search services
     */
    async search(query, params = {}) {
        return await this.getAll({
            search: query,
            ...params
        });
    },

    /**
     * Filter services by category
     */
    async getByCategory(categoryId, params = {}) {
        return await this.getAll({
            category: categoryId,
            ...params
        });
    },

    /**
     * Filter services by availability
     */
    async getAvailable(params = {}) {
        return await this.getAll({
            available: true,
            ...params
        });
    },

    /**
     * Filter services by duration range
     */
    async getByDurationRange(minDuration, maxDuration, params = {}) {
        return await this.getAll({
            min_duration: minDuration,
            max_duration: maxDuration,
            ...params
        });
    },
};

/**
 * Location API service functions
 */
export const locationService = {
    /**
     * Get all locations with optional filtering
     */
    async getAll(params = {}) {
        return await api.locations.get('', {params});
    },

    /**
     * Get location by ID
     */
    async getById(id) {
        return await api.locations.get(`${id}/`);
    },

    /**
     * Create new location
     */
    async create(data) {
        return await api.locations.post('', data);
    },

    /**
     * Update location
     */
    async update(id, data) {
        return await api.locations.put(`${id}/`, data);
    },

    /**
     * Delete location
     */
    async delete(id) {
        return await api.locations.delete(`${id}/`);
    },

    /**
     * Activate location
     */
    async activate(id) {
        return await api.locations.post(`${id}/activate/`);
    },

    /**
     * Deactivate location
     */
    async deactivate(id) {
        return await api.locations.post(`${id}/deactivate/`);
    },

    /**
     * Bulk activate locations
     */
    async bulkActivate(locationIds) {
        return await api.locations.post('bulk_activate/', {
            location_ids: locationIds
        });
    },

    /**
     * Bulk deactivate locations
     */
    async bulkDeactivate(locationIds) {
        return await api.locations.post('bulk_deactivate/', {
            location_ids: locationIds
        });
    },

    /**
     * Search locations
     */
    async search(query, params = {}) {
        return await this.getAll({
            search: query,
            ...params
        });
    },

    /**
     * Filter locations by category
     */
    async getByCategory(categoryId, params = {}) {
        return await this.getAll({
            category: categoryId,
            ...params
        });
    },

    /**
     * Filter locations by city
     */
    async getByCity(city, params = {}) {
        return await this.getAll({
            city: city,
            ...params
        });
    },

    /**
     * Filter locations by availability
     */
    async getAvailable(params = {}) {
        return await this.getAll({
            available: true,
            ...params
        });
    },

    /**
     * Filter locations by capacity range
     */
    async getByCapacityRange(minCapacity, maxCapacity, params = {}) {
        return await this.getAll({
            min_capacity: minCapacity,
            max_capacity: maxCapacity,
            ...params
        });
    },

    /**
     * Get list of unique cities
     */
    async getCities() {
        return await api.locations.get('cities/');
    },
};
