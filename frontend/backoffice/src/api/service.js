import api from '@/api/index.js';

/**
 * Service API service functions
 */
export const serviceService = {
    /**
     * Get all services with optional filtering
     */
    async getAll(params = {}) {
        return await api.services.get('', {params});
    },

    /**
     * Get service by ID
     */
    async getById(id) {
        return await api.services.get(`${id}/`);
    },

    /**
     * Create new service
     */
    async create(data) {
        return await api.services.post('', data);
    },

    /**
     * Update service
     */
    async update(id, data) {
        return await api.services.put(`${id}/`, data);
    },

    /**
     * Delete service
     */
    async delete(id) {
        return await api.services.delete(`${id}/`);
    },

    /**
     * Activate service
     */
    async activate(id) {
        return await api.services.post(`${id}/activate/`);
    },

    /**
     * Deactivate service
     */
    async deactivate(id) {
        return await api.services.post(`${id}/deactivate/`);
    },

    /**
     * Bulk activate services
     */
    async bulkActivate(serviceIds) {
        return await api.services.post('bulk_activate/', {
            service_ids: serviceIds
        });
    },

    /**
     * Bulk deactivate services
     */
    async bulkDeactivate(serviceIds) {
        return await api.services.post('bulk_deactivate/', {
            service_ids: serviceIds
        });
    },

    /**
     * Search services
     */
    async search(query, params = {}) {
        return await this.getAll({
            search: query,
            ...params
        });
    },

    /**
     * Filter services by category
     */
    async getByCategory(categoryId, params = {}) {
        return await this.getAll({
            category: categoryId,
            ...params
        });
    },

    /**
     * Filter services by availability
     */
    async getAvailable(params = {}) {
        return await this.getAll({
            available: true,
            ...params
        });
    },

    /**
     * Filter services by duration range
     */
    async getByDurationRange(minDuration, maxDuration, params = {}) {
        return await this.getAll({
            min_duration: minDuration,
            max_duration: maxDuration,
            ...params
        });
    },
};
