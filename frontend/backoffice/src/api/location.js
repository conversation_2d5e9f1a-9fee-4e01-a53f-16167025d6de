import api from '@/api/index.js';

/**
 * Location API service functions
 */
export const locationService = {
    /**
     * Get all locations with optional filtering
     */
    async getAll(params = {}) {
        return await api.locations.get('', {params});
    },

    /**
     * Get location by ID
     */
    async getById(id) {
        return await api.locations.get(`${id}/`);
    },

    /**
     * Create new location
     */
    async create(data) {
        return await api.locations.post('', data);
    },

    /**
     * Update location
     */
    async update(id, data) {
        return await api.locations.put(`${id}/`, data);
    },

    /**
     * Delete location
     */
    async delete(id) {
        return await api.locations.delete(`${id}/`);
    },

    /**
     * Activate location
     */
    async activate(id) {
        return await api.locations.post(`${id}/activate/`);
    },

    /**
     * Deactivate location
     */
    async deactivate(id) {
        return await api.locations.post(`${id}/deactivate/`);
    },

    /**
     * Bulk activate locations
     */
    async bulkActivate(locationIds) {
        return await api.locations.post('bulk_activate/', {
            location_ids: locationIds
        });
    },

    /**
     * Bulk deactivate locations
     */
    async bulkDeactivate(locationIds) {
        return await api.locations.post('bulk_deactivate/', {
            location_ids: locationIds
        });
    },

    /**
     * Search locations
     */
    async search(query, params = {}) {
        return await this.getAll({
            search: query,
            ...params
        });
    },

    /**
     * Filter locations by category
     */
    async getByCategory(categoryId, params = {}) {
        return await this.getAll({
            category: categoryId,
            ...params
        });
    },

    /**
     * Filter locations by city
     */
    async getByCity(city, params = {}) {
        return await this.getAll({
            city: city,
            ...params
        });
    },

    /**
     * Filter locations by availability
     */
    async getAvailable(params = {}) {
        return await this.getAll({
            available: true,
            ...params
        });
    },

    /**
     * Filter locations by capacity range
     */
    async getByCapacityRange(minCapacity, maxCapacity, params = {}) {
        return await this.getAll({
            min_capacity: minCapacity,
            max_capacity: maxCapacity,
            ...params
        });
    },

    /**
     * Get list of unique cities
     */
    async getCities() {
        return await api.locations.get('cities/');
    },
};
