import {createRouter, CATCH_ALL_PATH, LOGIN_ROUTE_NAME} from '@common/router.js';
import {defineAsyncComponent} from 'vue';

// Define layouts in constants to avoid VueRout<PERSON> from reloading them
const AuthLayout = defineAsyncComponent(() => import('@/layouts/AuthLayout.vue'));
const FullPageLayout = defineAsyncComponent(() => import('@/layouts/FullPageLayout.vue'));

const routes = [
    {
        path: '/',
        component: () => import('@/views/DashboardView.vue'),
        name: 'dashboard',
    },
    {
        path: '/login',
        component: () => import('@/views/auth/LoginView.vue'),
        name: LOGIN_ROUTE_NAME,
        meta: {requiresAuth: false, layout: AuthLayout},
    },
    {
        path: '/customer',
        component: () => import('@/views/customer/CustomerListView.vue'),
        name: 'customer-list',
    },
    {
        path: '/customer/:id',
        component: () => import('@/views/customer/CustomerDetailView.vue'),
        name: 'customer-detail',
    },
    // Products routes
    {
        path: '/products',
        component: () => import('@/views/products/ProductListView.vue'),
        name: 'product-list',
    },
    {
        path: '/products/create',
        component: () => import('@/views/products/ProductFormView.vue'),
        name: 'product-create',
    },
    {
        path: '/products/:id',
        component: () => import('@/views/products/ProductDetailView.vue'),
        name: 'product-detail',
    },
    {
        path: '/products/:id/edit',
        component: () => import('@/views/products/ProductFormView.vue'),
        name: 'product-edit',
    },
    // Services routes
    {
        path: '/services',
        component: () => import('@/views/services/ServiceListView.vue'),
        name: 'service-list',
    },
    {
        path: '/services/create',
        component: () => import('@/views/services/ServiceFormView.vue'),
        name: 'service-create',
    },
    {
        path: '/services/:id',
        component: () => import('@/views/services/ServiceDetailView.vue'),
        name: 'service-detail',
    },
    {
        path: '/services/:id/edit',
        component: () => import('@/views/services/ServiceFormView.vue'),
        name: 'service-edit',
    },
    // Locations routes
    {
        path: '/locations',
        component: () => import('@/views/locations/LocationListView.vue'),
        name: 'location-list',
    },
    {
        path: '/locations/create',
        component: () => import('@/views/locations/LocationFormView.vue'),
        name: 'location-create',
    },
    {
        path: '/locations/:id',
        component: () => import('@/views/locations/LocationDetailView.vue'),
        name: 'location-detail',
    },
    {
        path: '/locations/:id/edit',
        component: () => import('@/views/locations/LocationFormView.vue'),
        name: 'location-edit',
    },
    // Categories routes
    {
        path: '/categories',
        component: () => import('@/views/categories/CategoryListView.vue'),
        name: 'category-list',
    },
    {
        path: '/categories/create',
        component: () => import('@/views/categories/CategoryFormView.vue'),
        name: 'category-create',
    },
    {
        path: '/categories/:id',
        component: () => import('@/views/categories/CategoryDetailView.vue'),
        name: 'category-detail',
    },
    {
        path: '/categories/:id/edit',
        component: () => import('@/views/categories/CategoryFormView.vue'),
        name: 'category-edit',
    },
    {
        path: CATCH_ALL_PATH,
        component: () => import('@/views/errors/NotFoundView.vue'),
        name: 'not-found',
        meta: {
            requiresAuth: false,
            layout: FullPageLayout,
        },
    },
];

const router = createRouter({
    routes,
});

function pushRouteOnAuthentication(isAuthenticated) {
    if (isAuthenticated) {
        router.push({name: 'dashboard'});
    } else {
        router.push({name: LOGIN_ROUTE_NAME});
    }
}

export default router;
export {pushRouteOnAuthentication};
