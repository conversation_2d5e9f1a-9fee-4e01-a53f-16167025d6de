<script setup>
    import {createClassVariance} from '@horizon/utils/class-variance';

    defineProps({
        inline: {
            type: Boolean,
            default: false,
        },
    });

    const getLogoClass = createClassVariance('', {
        variants: {
            inline: {
                [true]: 'inline-block',
            },
        },
    });
</script>

<template>
    <img :class="getLogoClass($props)" src="@common/assets/hatterparty-logo.png" alt="Costing" width="36" height="36" />
</template>
