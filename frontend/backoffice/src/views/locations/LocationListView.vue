<template>
    <div class="location-list-view">
        <div class="header-section">
            <h1>Locations</h1>
            <div class="header-actions">
                <button 
                    @click="$router.push('/locations/create')"
                    class="btn btn-primary"
                >
                    <i class="icon-plus"></i>
                    Add Location
                </button>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-section">
            <div class="filter-row">
                <div class="filter-group">
                    <label>Search</label>
                    <input 
                        v-model="filters.search"
                        type="text"
                        placeholder="Search locations..."
                        @input="debouncedSearch"
                        class="form-input"
                    />
                </div>
                
                <div class="filter-group">
                    <label>Category</label>
                    <select v-model="filters.category" @change="loadLocations" class="form-select">
                        <option value="">All Categories</option>
                        <option 
                            v-for="category in categories" 
                            :key="category.id" 
                            :value="category.id"
                        >
                            {{ category.name }}
                        </option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>City</label>
                    <select v-model="filters.city" @change="loadLocations" class="form-select">
                        <option value="">All Cities</option>
                        <option 
                            v-for="city in cities" 
                            :key="city" 
                            :value="city"
                        >
                            {{ city }}
                        </option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>Status</label>
                    <select v-model="filters.available" @change="loadLocations" class="form-select">
                        <option value="">All</option>
                        <option value="true">Active</option>
                        <option value="false">Inactive</option>
                    </select>
                </div>

                <div class="filter-group">
                    <button @click="clearFilters" class="btn btn-secondary">
                        Clear Filters
                    </button>
                </div>
            </div>
        </div>

        <!-- Bulk Actions -->
        <div v-if="selectedLocations.length > 0" class="bulk-actions">
            <span>{{ selectedLocations.length }} locations selected</span>
            <button @click="bulkActivate" class="btn btn-success">
                Activate Selected
            </button>
            <button @click="bulkDeactivate" class="btn btn-warning">
                Deactivate Selected
            </button>
        </div>

        <!-- Locations Table -->
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>
                            <input 
                                type="checkbox" 
                                @change="toggleSelectAll"
                                :checked="allSelected"
                            />
                        </th>
                        <th>Image</th>
                        <th @click="sort('name')" class="sortable">
                            Name
                            <i :class="getSortIcon('name')"></i>
                        </th>
                        <th @click="sort('city')" class="sortable">
                            City
                            <i :class="getSortIcon('city')"></i>
                        </th>
                        <th>Category</th>
                        <th @click="sort('price')" class="sortable">
                            Price
                            <i :class="getSortIcon('price')"></i>
                        </th>
                        <th @click="sort('capacity')" class="sortable">
                            Capacity
                            <i :class="getSortIcon('capacity')"></i>
                        </th>
                        <th>Amenities</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="location in locations" :key="location.id">
                        <td>
                            <input 
                                type="checkbox" 
                                :value="location.id"
                                v-model="selectedLocations"
                            />
                        </td>
                        <td>
                            <img 
                                v-if="location.image" 
                                :src="location.image" 
                                :alt="location.name"
                                class="location-thumbnail"
                            />
                            <div v-else class="no-image">No Image</div>
                        </td>
                        <td>
                            <div class="location-name">{{ location.name }}</div>
                            <div class="location-description">{{ location.short_description }}</div>
                        </td>
                        <td>{{ location.city }}</td>
                        <td>{{ location.category_name || '-' }}</td>
                        <td class="price">€{{ location.price }}</td>
                        <td>{{ location.capacity }}</td>
                        <td>
                            <div class="amenities">
                                <span v-if="location.has_parking" class="amenity-badge">Parking</span>
                                <span v-if="location.has_catering" class="amenity-badge">Catering</span>
                                <span v-if="location.indoor" class="amenity-badge">Indoor</span>
                                <span v-if="location.outdoor" class="amenity-badge">Outdoor</span>
                            </div>
                        </td>
                        <td>
                            <span 
                                :class="getStatusClass(location.availability_status)"
                                class="status-badge"
                            >
                                {{ location.availability_status }}
                            </span>
                        </td>
                        <td class="actions">
                            <button 
                                @click="viewLocation(location.id)"
                                class="btn btn-sm btn-info"
                                title="View"
                            >
                                <i class="icon-eye"></i>
                            </button>
                            <button 
                                @click="editLocation(location.id)"
                                class="btn btn-sm btn-primary"
                                title="Edit"
                            >
                                <i class="icon-edit"></i>
                            </button>
                            <button 
                                v-if="location.is_active"
                                @click="deactivateLocation(location.id)"
                                class="btn btn-sm btn-warning"
                                title="Deactivate"
                            >
                                <i class="icon-pause"></i>
                            </button>
                            <button 
                                v-else
                                @click="activateLocation(location.id)"
                                class="btn btn-sm btn-success"
                                title="Activate"
                            >
                                <i class="icon-play"></i>
                            </button>
                            <button 
                                @click="deleteLocation(location.id)"
                                class="btn btn-sm btn-danger"
                                title="Delete"
                            >
                                <i class="icon-trash"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div v-if="pagination.pages > 1" class="pagination">
            <button 
                @click="changePage(pagination.current - 1)"
                :disabled="pagination.current <= 1"
                class="btn btn-secondary"
            >
                Previous
            </button>
            
            <span class="page-info">
                Page {{ pagination.current }} of {{ pagination.pages }}
                ({{ pagination.count }} total)
            </span>
            
            <button 
                @click="changePage(pagination.current + 1)"
                :disabled="pagination.current >= pagination.pages"
                class="btn btn-secondary"
            >
                Next
            </button>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="loading">
            Loading locations...
        </div>

        <!-- Empty State -->
        <div v-if="!loading && locations.length === 0" class="empty-state">
            <h3>No locations found</h3>
            <p>Create your first location to get started.</p>
            <button 
                @click="$router.push('/locations/create')"
                class="btn btn-primary"
            >
                Add Location
            </button>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { locationService } from '@/api/location.js';
import { categoryService } from '@/api/category.js';
import { useToast } from '@horizon/composables/toast';
import { debounce } from 'lodash-es';

const router = useRouter();
const { showToast } = useToast();

// Reactive data
const locations = ref([]);
const categories = ref([]);
const cities = ref([]);
const selectedLocations = ref([]);
const loading = ref(false);

const filters = reactive({
    search: '',
    category: '',
    city: '',
    available: '',
});

const sorting = reactive({
    field: 'name',
    direction: 'asc',
});

const pagination = reactive({
    current: 1,
    pages: 1,
    count: 0,
    pageSize: 25,
});

// Computed properties
const allSelected = computed(() => {
    return locations.value.length > 0 && selectedLocations.value.length === locations.value.length;
});

// Methods
const loadLocations = async (page = 1) => {
    loading.value = true;
    try {
        const params = {
            page,
            page_size: pagination.pageSize,
            ordering: sorting.direction === 'desc' ? `-${sorting.field}` : sorting.field,
            ...filters,
        };

        // Remove empty filters
        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === null || params[key] === undefined) {
                delete params[key];
            }
        });

        const response = await locationService.getAll(params);
        
        locations.value = response.results || [];
        pagination.current = page;
        pagination.pages = response.pages || 1;
        pagination.count = response.count || 0;
    } catch (error) {
        showToast('Error loading locations', 'error');
        console.error('Error loading locations:', error);
    } finally {
        loading.value = false;
    }
};

const loadCategories = async () => {
    try {
        const response = await categoryService.getOptions();
        categories.value = response;
    } catch (error) {
        console.error('Error loading categories:', error);
    }
};

const loadCities = async () => {
    try {
        const response = await locationService.getCities();
        cities.value = response.cities || [];
    } catch (error) {
        console.error('Error loading cities:', error);
    }
};

const debouncedSearch = debounce(() => {
    loadLocations(1);
}, 300);

const clearFilters = () => {
    filters.search = '';
    filters.category = '';
    filters.city = '';
    filters.available = '';
    loadLocations(1);
};

const sort = (field) => {
    if (sorting.field === field) {
        sorting.direction = sorting.direction === 'asc' ? 'desc' : 'asc';
    } else {
        sorting.field = field;
        sorting.direction = 'asc';
    }
    loadLocations(1);
};

const getSortIcon = (field) => {
    if (sorting.field !== field) return 'icon-sort';
    return sorting.direction === 'asc' ? 'icon-sort-up' : 'icon-sort-down';
};

const getStatusClass = (status) => {
    if (status === 'Available') return 'status-success';
    if (status.includes('Inactive') || status.includes('Not valid')) return 'status-danger';
    return 'status-secondary';
};

const toggleSelectAll = () => {
    if (allSelected.value) {
        selectedLocations.value = [];
    } else {
        selectedLocations.value = locations.value.map(l => l.id);
    }
};

const changePage = (page) => {
    if (page >= 1 && page <= pagination.pages) {
        loadLocations(page);
    }
};

const viewLocation = (id) => {
    router.push(`/locations/${id}`);
};

const editLocation = (id) => {
    router.push(`/locations/${id}/edit`);
};

const activateLocation = async (id) => {
    try {
        await locationService.activate(id);
        showToast('Location activated successfully', 'success');
        loadLocations(pagination.current);
    } catch (error) {
        showToast('Error activating location', 'error');
        console.error('Error activating location:', error);
    }
};

const deactivateLocation = async (id) => {
    try {
        await locationService.deactivate(id);
        showToast('Location deactivated successfully', 'success');
        loadLocations(pagination.current);
    } catch (error) {
        showToast('Error deactivating location', 'error');
        console.error('Error deactivating location:', error);
    }
};

const deleteLocation = async (id) => {
    if (!confirm('Are you sure you want to delete this location?')) return;
    
    try {
        await locationService.delete(id);
        showToast('Location deleted successfully', 'success');
        loadLocations(pagination.current);
    } catch (error) {
        showToast('Error deleting location', 'error');
        console.error('Error deleting location:', error);
    }
};

const bulkActivate = async () => {
    try {
        await locationService.bulkActivate(selectedLocations.value);
        showToast(`${selectedLocations.value.length} locations activated`, 'success');
        selectedLocations.value = [];
        loadLocations(pagination.current);
    } catch (error) {
        showToast('Error activating locations', 'error');
        console.error('Error activating locations:', error);
    }
};

const bulkDeactivate = async () => {
    try {
        await locationService.bulkDeactivate(selectedLocations.value);
        showToast(`${selectedLocations.value.length} locations deactivated`, 'success');
        selectedLocations.value = [];
        loadLocations(pagination.current);
    } catch (error) {
        showToast('Error deactivating locations', 'error');
        console.error('Error deactivating locations:', error);
    }
};

// Lifecycle
onMounted(() => {
    loadLocations();
    loadCategories();
    loadCities();
});
</script>

<style scoped>
/* Location list specific styles - reusing ProductListView patterns */
.location-list-view {
    padding: 1rem;
}

.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.filters-section {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filter-row {
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 200px;
}

.filter-group label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #374151;
}

.bulk-actions {
    background: #f3f4f6;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.data-table th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
}

.sortable {
    cursor: pointer;
    user-select: none;
}

.sortable:hover {
    background: #f3f4f6;
}

.location-thumbnail {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
}

.no-image {
    width: 50px;
    height: 50px;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    color: #6b7280;
    border-radius: 4px;
}

.location-name {
    font-weight: 500;
    color: #111827;
}

.location-description {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.price {
    font-weight: 600;
    color: #059669;
}

.amenities {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.amenity-badge {
    background: #e0e7ff;
    color: #3730a3;
    padding: 0.125rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-success {
    background: #d1fae5;
    color: #065f46;
}

.status-danger {
    background: #fee2e2;
    color: #991b1b;
}

.status-secondary {
    background: #f3f4f6;
    color: #374151;
}

.actions {
    display: flex;
    gap: 0.5rem;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
}

.page-info {
    color: #6b7280;
}

.loading {
    text-align: center;
    padding: 2rem;
    color: #6b7280;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;
}

.empty-state h3 {
    color: #374151;
    margin-bottom: 0.5rem;
}

.empty-state p {
    margin-bottom: 2rem;
}
</style>
