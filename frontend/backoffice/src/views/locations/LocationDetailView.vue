<template>
    <div class="location-detail-view">
        <div class="header-section">
            <div class="header-left">
                <button @click="$router.back()" class="btn btn-secondary">
                    <i class="icon-arrow-left"></i>
                    Back
                </button>
                <h1>{{ location?.name || 'Location Details' }}</h1>
            </div>
            <div class="header-actions">
                <button @click="editLocation" class="btn btn-primary">
                    <i class="icon-edit"></i>
                    Edit
                </button>
                <button 
                    v-if="location?.is_active"
                    @click="deactivateLocation"
                    class="btn btn-warning"
                >
                    <i class="icon-pause"></i>
                    Deactivate
                </button>
                <button 
                    v-else
                    @click="activateLocation"
                    class="btn btn-success"
                >
                    <i class="icon-play"></i>
                    Activate
                </button>
                <button @click="deleteLocation" class="btn btn-danger">
                    <i class="icon-trash"></i>
                    Delete
                </button>
            </div>
        </div>

        <div v-if="loading" class="loading">
            Loading location details...
        </div>

        <div v-else-if="location" class="location-details">
            <!-- Status Banner -->
            <div v-if="!location.is_available" class="status-banner warning">
                <i class="icon-warning"></i>
                This location is not currently available: {{ location.availability_status }}
            </div>

            <div class="details-grid">
                <!-- Main Information -->
                <div class="main-info">
                    <div class="location-image">
                        <img 
                            v-if="location.image" 
                            :src="location.image" 
                            :alt="location.name"
                            class="location-img"
                        />
                        <div v-else class="no-image">
                            <i class="icon-image"></i>
                            <span>No Image</span>
                        </div>
                    </div>

                    <div class="location-info">
                        <div class="info-section">
                            <h2>Basic Information</h2>
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>Name</label>
                                    <span>{{ location.name }}</span>
                                </div>
                                <div class="info-item">
                                    <label>Category</label>
                                    <span>{{ location.category_name || '-' }}</span>
                                </div>
                                <div class="info-item">
                                    <label>Status</label>
                                    <span 
                                        :class="getStatusClass(location.availability_status)"
                                        class="status-badge"
                                    >
                                        {{ location.availability_status }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="info-section">
                            <h3>Address</h3>
                            <p class="address">{{ location.full_address }}</p>
                        </div>

                        <div v-if="location.short_description" class="info-section">
                            <h3>Short Description</h3>
                            <p>{{ location.short_description }}</p>
                        </div>

                        <div v-if="location.description" class="info-section">
                            <h3>Description</h3>
                            <p class="description">{{ location.description }}</p>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Information -->
                <div class="sidebar-info">
                    <!-- Location Details -->
                    <div class="info-card">
                        <h3>Location Details</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Price per Day</label>
                                <span class="price">€{{ location.price }}</span>
                            </div>
                            <div class="info-item">
                                <label>Capacity</label>
                                <span>{{ location.capacity }} people</span>
                            </div>
                            <div class="info-item">
                                <label>City</label>
                                <span>{{ location.city }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Amenities -->
                    <div class="info-card">
                        <h3>Amenities</h3>
                        <div class="amenities-grid">
                            <div class="amenity-item">
                                <span :class="location.has_parking ? 'text-success' : 'text-muted'">
                                    <i :class="location.has_parking ? 'icon-check' : 'icon-x'"></i>
                                    Parking
                                </span>
                            </div>
                            <div class="amenity-item">
                                <span :class="location.has_catering ? 'text-success' : 'text-muted'">
                                    <i :class="location.has_catering ? 'icon-check' : 'icon-x'"></i>
                                    Catering
                                </span>
                            </div>
                            <div class="amenity-item">
                                <span :class="location.indoor ? 'text-success' : 'text-muted'">
                                    <i :class="location.indoor ? 'icon-check' : 'icon-x'"></i>
                                    Indoor Space
                                </span>
                            </div>
                            <div class="amenity-item">
                                <span :class="location.outdoor ? 'text-success' : 'text-muted'">
                                    <i :class="location.outdoor ? 'icon-check' : 'icon-x'"></i>
                                    Outdoor Space
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Temporal Validity -->
                    <div class="info-card">
                        <h3>Temporal Validity</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Valid From</label>
                                <span>{{ formatDateTime(location.valid_from) || 'Not set' }}</span>
                            </div>
                            <div class="info-item">
                                <label>Valid Until</label>
                                <span>{{ formatDateTime(location.valid_until) || 'Not set' }}</span>
                            </div>
                            <div class="info-item">
                                <label>Currently Valid</label>
                                <span :class="location.is_currently_valid ? 'text-success' : 'text-danger'">
                                    {{ location.is_currently_valid ? 'Yes' : 'No' }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Activation -->
                    <div class="info-card">
                        <h3>Activation</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Active</label>
                                <span :class="location.is_active ? 'text-success' : 'text-danger'">
                                    {{ location.is_active ? 'Yes' : 'No' }}
                                </span>
                            </div>
                            <div v-if="location.activated_at" class="info-item">
                                <label>Activated At</label>
                                <span>{{ formatDateTime(location.activated_at) }}</span>
                            </div>
                            <div v-if="location.deactivated_at" class="info-item">
                                <label>Deactivated At</label>
                                <span>{{ formatDateTime(location.deactivated_at) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Timestamps -->
                    <div class="info-card">
                        <h3>Timestamps</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Created</label>
                                <span>{{ formatDateTime(location.created_at) }}</span>
                            </div>
                            <div class="info-item">
                                <label>Modified</label>
                                <span>{{ formatDateTime(location.modified_at) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-else-if="!loading" class="error-state">
            <h3>Location not found</h3>
            <p>The requested location could not be found.</p>
            <button @click="$router.push('/locations')" class="btn btn-primary">
                Back to Locations
            </button>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { locationService } from '@/api/location.js';
import { useToast } from '@horizon/composables/toast';

const route = useRoute();
const router = useRouter();
const { showToast } = useToast();

// Reactive data
const location = ref(null);
const loading = ref(false);

// Methods
const loadLocation = async () => {
    loading.value = true;
    try {
        const response = await locationService.getById(route.params.id);
        location.value = response;
    } catch (error) {
        showToast('Error loading location', 'error');
        console.error('Error loading location:', error);
    } finally {
        loading.value = false;
    }
};

const editLocation = () => {
    router.push(`/locations/${route.params.id}/edit`);
};

const activateLocation = async () => {
    try {
        await locationService.activate(route.params.id);
        showToast('Location activated successfully', 'success');
        loadLocation();
    } catch (error) {
        showToast('Error activating location', 'error');
        console.error('Error activating location:', error);
    }
};

const deactivateLocation = async () => {
    try {
        await locationService.deactivate(route.params.id);
        showToast('Location deactivated successfully', 'success');
        loadLocation();
    } catch (error) {
        showToast('Error deactivating location', 'error');
        console.error('Error deactivating location:', error);
    }
};

const deleteLocation = async () => {
    if (!confirm('Are you sure you want to delete this location? This action cannot be undone.')) {
        return;
    }
    
    try {
        await locationService.delete(route.params.id);
        showToast('Location deleted successfully', 'success');
        router.push('/locations');
    } catch (error) {
        showToast('Error deleting location', 'error');
        console.error('Error deleting location:', error);
    }
};

const getStatusClass = (status) => {
    if (status === 'Available') return 'status-success';
    if (status.includes('Inactive') || status.includes('Not valid')) return 'status-danger';
    return 'status-secondary';
};

const formatDateTime = (dateString) => {
    if (!dateString) return null;
    return new Date(dateString).toLocaleString();
};

// Lifecycle
onMounted(() => {
    loadLocation();
});
</script>

<style scoped>
/* Location detail specific styles - reusing detail view patterns */
.location-detail-view {
    padding: 1rem;
    max-width: 1400px;
    margin: 0 auto;
}

.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.status-banner {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    color: #92400e;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.details-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.main-info {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.location-image {
    margin-bottom: 2rem;
}

.location-img {
    width: 100%;
    max-width: 400px;
    height: 300px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.no-image {
    width: 100%;
    max-width: 400px;
    height: 300px;
    background: #f9fafb;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    font-size: 1.125rem;
}

.no-image i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.info-section {
    margin-bottom: 2rem;
}

.info-section:last-child {
    margin-bottom: 0;
}

.info-section h2 {
    color: #111827;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.info-section h3 {
    color: #374151;
    margin-bottom: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
}

.address {
    line-height: 1.6;
    color: #4b5563;
}

.description {
    line-height: 1.6;
    color: #4b5563;
    white-space: pre-wrap;
}

.sidebar-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.info-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-card h3 {
    color: #374151;
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.5rem;
}

.info-grid {
    display: grid;
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-item label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
}

.info-item span {
    color: #111827;
}

.price {
    font-size: 1.25rem;
    font-weight: 600;
    color: #059669;
}

.amenities-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.amenity-item span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-block;
    width: fit-content;
}

.status-success {
    background: #d1fae5;
    color: #065f46;
}

.status-danger {
    background: #fee2e2;
    color: #991b1b;
}

.status-secondary {
    background: #f3f4f6;
    color: #374151;
}

.text-success {
    color: #059669;
    font-weight: 500;
}

.text-danger {
    color: #dc2626;
    font-weight: 500;
}

.text-muted {
    color: #6b7280;
}

.loading {
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;
}

.error-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;
}

.error-state h3 {
    color: #374151;
    margin-bottom: 0.5rem;
}

.error-state p {
    margin-bottom: 2rem;
}

@media (max-width: 1024px) {
    .details-grid {
        grid-template-columns: 1fr;
    }
    
    .header-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .header-actions {
        flex-wrap: wrap;
    }
}

@media (max-width: 640px) {
    .header-left {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .header-actions {
        width: 100%;
        justify-content: flex-start;
    }
    
    .amenities-grid {
        grid-template-columns: 1fr;
    }
}
</style>
