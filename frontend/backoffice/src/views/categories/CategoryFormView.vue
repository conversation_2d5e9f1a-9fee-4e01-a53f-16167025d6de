<template>
    <div class="category-form-view">
        <div class="header-section">
            <h1>{{ isEdit ? 'Edit Category' : 'Create Category' }}</h1>
            <div class="header-actions">
                <button @click="$router.back()" class="btn btn-secondary">
                    <i class="icon-arrow-left"></i>
                    Back
                </button>
            </div>
        </div>

        <form @submit.prevent="saveCategory" class="category-form">
            <div class="form-section">
                <h3>Category Information</h3>
                
                <div class="form-group">
                    <label for="name" class="required">Name</label>
                    <input
                        id="name"
                        v-model="form.name"
                        type="text"
                        class="form-input"
                        :class="{ 'error': errors.name }"
                        required
                        @input="generateSlug"
                    />
                    <span v-if="errors.name" class="error-message">{{ errors.name[0] }}</span>
                </div>

                <div class="form-group">
                    <label for="slug" class="required">Slug</label>
                    <input
                        id="slug"
                        v-model="form.slug"
                        type="text"
                        class="form-input"
                        :class="{ 'error': errors.slug }"
                        required
                        pattern="[a-z0-9-]+"
                    />
                    <small class="help-text">
                        URL-friendly identifier (lowercase letters, numbers, and hyphens only)
                    </small>
                    <span v-if="errors.slug" class="error-message">{{ errors.slug[0] }}</span>
                </div>

                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea
                        id="description"
                        v-model="form.description"
                        class="form-textarea"
                        :class="{ 'error': errors.description }"
                        rows="6"
                        placeholder="Describe this category..."
                    ></textarea>
                    <span v-if="errors.description" class="error-message">{{ errors.description[0] }}</span>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <button type="button" @click="$router.back()" class="btn btn-secondary">
                    Cancel
                </button>
                <button type="submit" :disabled="loading" class="btn btn-primary">
                    <span v-if="loading">Saving...</span>
                    <span v-else>{{ isEdit ? 'Update Category' : 'Create Category' }}</span>
                </button>
            </div>
        </form>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { categoryService } from '@/api/category.js';
import { useToast } from '@horizon/composables/toast';

const route = useRoute();
const router = useRouter();
const { showToast } = useToast();

// Reactive data
const loading = ref(false);
const errors = ref({});

const form = reactive({
    name: '',
    slug: '',
    description: '',
});

// Computed properties
const isEdit = computed(() => !!route.params.id);
const categoryId = computed(() => route.params.id);

// Methods
const loadCategory = async () => {
    if (!isEdit.value) return;
    
    loading.value = true;
    try {
        const category = await categoryService.getById(categoryId.value);
        
        // Populate form with category data
        Object.keys(form).forEach(key => {
            if (category[key] !== undefined) {
                form[key] = category[key];
            }
        });
    } catch (error) {
        showToast('Error loading category', 'error');
        console.error('Error loading category:', error);
        router.push('/categories');
    } finally {
        loading.value = false;
    }
};

const generateSlug = () => {
    if (!isEdit.value && form.name) {
        form.slug = form.name
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim();
    }
};

const saveCategory = async () => {
    loading.value = true;
    errors.value = {};

    try {
        let response;
        if (isEdit.value) {
            response = await categoryService.update(categoryId.value, form);
        } else {
            response = await categoryService.create(form);
        }

        showToast(
            `Category ${isEdit.value ? 'updated' : 'created'} successfully`,
            'success'
        );
        
        router.push(`/categories/${response.id}`);
    } catch (error) {
        if (error.response?.data) {
            errors.value = error.response.data;
        }
        showToast(
            `Error ${isEdit.value ? 'updating' : 'creating'} category`,
            'error'
        );
        console.error('Error saving category:', error);
    } finally {
        loading.value = false;
    }
};

// Lifecycle
onMounted(() => {
    loadCategory();
});
</script>

<style scoped>
.category-form-view {
    padding: 1rem;
    max-width: 800px;
    margin: 0 auto;
}

.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.category-form {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-section {
    margin-bottom: 2rem;
}

.form-section h3 {
    color: #374151;
    margin-bottom: 1.5rem;
    font-size: 1.125rem;
    font-weight: 600;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #374151;
}

.form-group label.required::after {
    content: ' *';
    color: #ef4444;
}

.form-input,
.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error,
.form-textarea.error {
    border-color: #ef4444;
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.error-message {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
}

.help-text {
    color: #6b7280;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
    .header-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>
