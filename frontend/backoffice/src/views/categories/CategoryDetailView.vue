<template>
    <div class="category-detail-view">
        <div class="header-section">
            <div class="header-left">
                <button @click="$router.back()" class="btn btn-secondary">
                    <i class="icon-arrow-left"></i>
                    Back
                </button>
                <h1>{{ category?.name || 'Category Details' }}</h1>
            </div>
            <div class="header-actions">
                <button @click="editCategory" class="btn btn-primary">
                    <i class="icon-edit"></i>
                    Edit
                </button>
                <button @click="deleteCategory" class="btn btn-danger">
                    <i class="icon-trash"></i>
                    Delete
                </button>
            </div>
        </div>

        <div v-if="loading" class="loading">
            Loading category details...
        </div>

        <div v-else-if="category" class="category-details">
            <div class="details-grid">
                <!-- Main Information -->
                <div class="main-info">
                    <div class="info-section">
                        <h2>Category Information</h2>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Name</label>
                                <span class="category-name">{{ category.name }}</span>
                            </div>
                            <div class="info-item">
                                <label>Slug</label>
                                <code class="slug">{{ category.slug }}</code>
                            </div>
                        </div>
                    </div>

                    <div v-if="category.description" class="info-section">
                        <h3>Description</h3>
                        <p class="description">{{ category.description }}</p>
                    </div>

                    <!-- Usage Statistics -->
                    <div class="info-section">
                        <h3>Usage Statistics</h3>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">{{ category.product_count || 0 }}</div>
                                <div class="stat-label">Products</div>
                                <button 
                                    v-if="category.product_count > 0"
                                    @click="viewProducts"
                                    class="stat-link"
                                >
                                    View Products
                                </button>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">{{ category.service_count || 0 }}</div>
                                <div class="stat-label">Services</div>
                                <button 
                                    v-if="category.service_count > 0"
                                    @click="viewServices"
                                    class="stat-link"
                                >
                                    View Services
                                </button>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">{{ category.location_count || 0 }}</div>
                                <div class="stat-label">Locations</div>
                                <button 
                                    v-if="category.location_count > 0"
                                    @click="viewLocations"
                                    class="stat-link"
                                >
                                    View Locations
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Information -->
                <div class="sidebar-info">
                    <!-- Timestamps -->
                    <div class="info-card">
                        <h3>Timestamps</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Created</label>
                                <span>{{ formatDateTime(category.created_at) }}</span>
                            </div>
                            <div class="info-item">
                                <label>Modified</label>
                                <span>{{ formatDateTime(category.modified_at) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="info-card">
                        <h3>Quick Actions</h3>
                        <div class="action-buttons">
                            <button 
                                @click="$router.push('/products/create?category=' + category.id)"
                                class="btn btn-outline btn-sm"
                            >
                                <i class="icon-plus"></i>
                                Add Product
                            </button>
                            <button 
                                @click="$router.push('/services/create?category=' + category.id)"
                                class="btn btn-outline btn-sm"
                            >
                                <i class="icon-plus"></i>
                                Add Service
                            </button>
                            <button 
                                @click="$router.push('/locations/create?category=' + category.id)"
                                class="btn btn-outline btn-sm"
                            >
                                <i class="icon-plus"></i>
                                Add Location
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-else-if="!loading" class="error-state">
            <h3>Category not found</h3>
            <p>The requested category could not be found.</p>
            <button @click="$router.push('/categories')" class="btn btn-primary">
                Back to Categories
            </button>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { categoryService } from '@/api/category.js';
import { useToast } from '@horizon/composables/toast';

const route = useRoute();
const router = useRouter();
const { showToast } = useToast();

// Reactive data
const category = ref(null);
const loading = ref(false);

// Methods
const loadCategory = async () => {
    loading.value = true;
    try {
        const response = await categoryService.getById(route.params.id);
        category.value = response;
    } catch (error) {
        showToast('Error loading category', 'error');
        console.error('Error loading category:', error);
    } finally {
        loading.value = false;
    }
};

const editCategory = () => {
    router.push(`/categories/${route.params.id}/edit`);
};

const deleteCategory = async () => {
    if (!confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
        return;
    }
    
    try {
        await categoryService.delete(route.params.id);
        showToast('Category deleted successfully', 'success');
        router.push('/categories');
    } catch (error) {
        showToast('Error deleting category', 'error');
        console.error('Error deleting category:', error);
    }
};

const viewProducts = () => {
    router.push(`/products?category=${category.value.id}`);
};

const viewServices = () => {
    router.push(`/services?category=${category.value.id}`);
};

const viewLocations = () => {
    router.push(`/locations?category=${category.value.id}`);
};

const formatDateTime = (dateString) => {
    if (!dateString) return null;
    return new Date(dateString).toLocaleString();
};

// Lifecycle
onMounted(() => {
    loadCategory();
});
</script>

<style scoped>
.category-detail-view {
    padding: 1rem;
    max-width: 1200px;
    margin: 0 auto;
}

.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.details-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.main-info {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-section {
    margin-bottom: 2rem;
}

.info-section:last-child {
    margin-bottom: 0;
}

.info-section h2 {
    color: #111827;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.info-section h3 {
    color: #374151;
    margin-bottom: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
}

.info-grid {
    display: grid;
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-item label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
}

.info-item span {
    color: #111827;
}

.category-name {
    font-size: 1.125rem;
    font-weight: 600;
}

.slug {
    background: #f3f4f6;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    color: #6b7280;
    display: inline-block;
}

.description {
    line-height: 1.6;
    color: #4b5563;
    white-space: pre-wrap;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.stat-card {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #059669;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6b7280;
    font-weight: 500;
    margin-bottom: 1rem;
}

.stat-link {
    background: none;
    border: none;
    color: #3b82f6;
    text-decoration: underline;
    cursor: pointer;
    font-size: 0.875rem;
}

.stat-link:hover {
    color: #1d4ed8;
}

.sidebar-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.info-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-card h3 {
    color: #374151;
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.5rem;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.loading {
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;
}

.error-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;
}

.error-state h3 {
    color: #374151;
    margin-bottom: 0.5rem;
}

.error-state p {
    margin-bottom: 2rem;
}

@media (max-width: 1024px) {
    .details-grid {
        grid-template-columns: 1fr;
    }
    
    .header-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .header-actions {
        flex-wrap: wrap;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 640px) {
    .header-left {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .header-actions {
        width: 100%;
        justify-content: flex-start;
    }
}
</style>
