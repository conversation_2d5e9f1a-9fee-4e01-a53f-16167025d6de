<template>
    <div class="category-list-view">
        <div class="header-section">
            <h1>Categories</h1>
            <div class="header-actions">
                <button 
                    @click="$router.push('/categories/create')"
                    class="btn btn-primary"
                >
                    <i class="icon-plus"></i>
                    Add Category
                </button>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-section">
            <div class="filter-row">
                <div class="filter-group">
                    <label>Search</label>
                    <input 
                        v-model="filters.search"
                        type="text"
                        placeholder="Search categories..."
                        @input="debouncedSearch"
                        class="form-input"
                    />
                </div>
                
                <div class="filter-group">
                    <button @click="clearFilters" class="btn btn-secondary">
                        Clear Filters
                    </button>
                </div>
            </div>
        </div>

        <!-- Categories Table -->
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th @click="sort('name')" class="sortable">
                            Name
                            <i :class="getSortIcon('name')"></i>
                        </th>
                        <th>Slug</th>
                        <th>Description</th>
                        <th>Products</th>
                        <th>Services</th>
                        <th>Locations</th>
                        <th @click="sort('created_at')" class="sortable">
                            Created
                            <i :class="getSortIcon('created_at')"></i>
                        </th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="category in categories" :key="category.id">
                        <td>
                            <div class="category-name">{{ category.name }}</div>
                        </td>
                        <td>
                            <code class="slug">{{ category.slug }}</code>
                        </td>
                        <td>
                            <div class="description">
                                {{ category.description ? (category.description.length > 100 ? category.description.substring(0, 100) + '...' : category.description) : '-' }}
                            </div>
                        </td>
                        <td class="count">{{ category.product_count || 0 }}</td>
                        <td class="count">{{ category.service_count || 0 }}</td>
                        <td class="count">{{ category.location_count || 0 }}</td>
                        <td>{{ formatDate(category.created_at) }}</td>
                        <td class="actions">
                            <button 
                                @click="viewCategory(category.id)"
                                class="btn btn-sm btn-info"
                                title="View"
                            >
                                <i class="icon-eye"></i>
                            </button>
                            <button 
                                @click="editCategory(category.id)"
                                class="btn btn-sm btn-primary"
                                title="Edit"
                            >
                                <i class="icon-edit"></i>
                            </button>
                            <button 
                                @click="deleteCategory(category.id)"
                                class="btn btn-sm btn-danger"
                                title="Delete"
                            >
                                <i class="icon-trash"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div v-if="pagination.pages > 1" class="pagination">
            <button 
                @click="changePage(pagination.current - 1)"
                :disabled="pagination.current <= 1"
                class="btn btn-secondary"
            >
                Previous
            </button>
            
            <span class="page-info">
                Page {{ pagination.current }} of {{ pagination.pages }}
                ({{ pagination.count }} total)
            </span>
            
            <button 
                @click="changePage(pagination.current + 1)"
                :disabled="pagination.current >= pagination.pages"
                class="btn btn-secondary"
            >
                Next
            </button>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="loading">
            Loading categories...
        </div>

        <!-- Empty State -->
        <div v-if="!loading && categories.length === 0" class="empty-state">
            <h3>No categories found</h3>
            <p>Create your first category to get started.</p>
            <button 
                @click="$router.push('/categories/create')"
                class="btn btn-primary"
            >
                Add Category
            </button>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { categoryService } from '@/api/category.js';
import { useToast } from '@horizon/composables/toast';
import { debounce } from 'lodash-es';

const router = useRouter();
const { showToast } = useToast();

// Reactive data
const categories = ref([]);
const loading = ref(false);

const filters = reactive({
    search: '',
});

const sorting = reactive({
    field: 'name',
    direction: 'asc',
});

const pagination = reactive({
    current: 1,
    pages: 1,
    count: 0,
    pageSize: 25,
});

// Methods
const loadCategories = async (page = 1) => {
    loading.value = true;
    try {
        const params = {
            page,
            page_size: pagination.pageSize,
            ordering: sorting.direction === 'desc' ? `-${sorting.field}` : sorting.field,
            ...filters,
        };

        // Remove empty filters
        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === null || params[key] === undefined) {
                delete params[key];
            }
        });

        const response = await categoryService.getAll(params);
        
        categories.value = response.results || [];
        pagination.current = page;
        pagination.pages = response.pages || 1;
        pagination.count = response.count || 0;
    } catch (error) {
        showToast('Error loading categories', 'error');
        console.error('Error loading categories:', error);
    } finally {
        loading.value = false;
    }
};

const debouncedSearch = debounce(() => {
    loadCategories(1);
}, 300);

const clearFilters = () => {
    filters.search = '';
    loadCategories(1);
};

const sort = (field) => {
    if (sorting.field === field) {
        sorting.direction = sorting.direction === 'asc' ? 'desc' : 'asc';
    } else {
        sorting.field = field;
        sorting.direction = 'asc';
    }
    loadCategories(1);
};

const getSortIcon = (field) => {
    if (sorting.field !== field) return 'icon-sort';
    return sorting.direction === 'asc' ? 'icon-sort-up' : 'icon-sort-down';
};

const changePage = (page) => {
    if (page >= 1 && page <= pagination.pages) {
        loadCategories(page);
    }
};

const viewCategory = (id) => {
    router.push(`/categories/${id}`);
};

const editCategory = (id) => {
    router.push(`/categories/${id}/edit`);
};

const deleteCategory = async (id) => {
    if (!confirm('Are you sure you want to delete this category?')) return;
    
    try {
        await categoryService.delete(id);
        showToast('Category deleted successfully', 'success');
        loadCategories(pagination.current);
    } catch (error) {
        showToast('Error deleting category', 'error');
        console.error('Error deleting category:', error);
    }
};

const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString();
};

// Lifecycle
onMounted(() => {
    loadCategories();
});
</script>

<style scoped>
/* Category list specific styles */
.category-list-view {
    padding: 1rem;
}

.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.filters-section {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filter-row {
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 200px;
}

.filter-group label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #374151;
}

.table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.data-table th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
}

.sortable {
    cursor: pointer;
    user-select: none;
}

.sortable:hover {
    background: #f3f4f6;
}

.category-name {
    font-weight: 500;
    color: #111827;
}

.slug {
    background: #f3f4f6;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    color: #6b7280;
}

.description {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.4;
}

.count {
    font-weight: 600;
    color: #059669;
    text-align: center;
}

.actions {
    display: flex;
    gap: 0.5rem;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
}

.page-info {
    color: #6b7280;
}

.loading {
    text-align: center;
    padding: 2rem;
    color: #6b7280;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;
}

.empty-state h3 {
    color: #374151;
    margin-bottom: 0.5rem;
}

.empty-state p {
    margin-bottom: 2rem;
}
</style>
