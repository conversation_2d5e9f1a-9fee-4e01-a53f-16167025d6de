<script setup>
    import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, HGenericError, HTypo} from '@horizon/components';
    import {useAuthStore} from '@common/stores/auth';
    import event from '@common/event';
    import v from '@common/form/validator';

    import HatterPartyLogo from '@/components/hatterparty/HatterPartyLogo.vue';

    const router = useRouter();
    const {login} = useAuthStore();

    const formData = ref({
        email: '<EMAIL>',
        password: 'hatterparty',
    });
    const formFields = computed(() => ({
        email: {type: 'text', label: 'Email', validation: v.string().min(6)},
        password: {type: 'password', label: 'Password', validation: v.string().min(6)},
    }));

    event.on('authenticated', () => {
        router.push({name: 'dashboard'});
    });
</script>

<template>
    <HForm v-model="formData" :fields="formFields" :submit-function="login">
        <template #default="{loading, submit}">
            <HTypo type="page-title">
                <HatterPartyLogo inline />
                Login
            </HTypo>
            <HField name="email" />
            <HField name="password" />
            <HGenericError name="detail" />
            <div class="flex">
                <HButton class="ml-auto" :loading="loading" @click="submit">Login</HButton>
            </div>
        </template>
    </HForm>
</template>
