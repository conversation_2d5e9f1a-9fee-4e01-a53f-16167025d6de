<template>
    <div class="product-detail-view">
        <div class="header-section">
            <div class="header-left">
                <button @click="$router.back()" class="btn btn-secondary">
                    <i class="icon-arrow-left"></i>
                    Back
                </button>
                <h1>{{ product?.name || 'Product Details' }}</h1>
            </div>
            <div class="header-actions">
                <button @click="editProduct" class="btn btn-primary">
                    <i class="icon-edit"></i>
                    Edit
                </button>
                <button v-if="product?.is_active" @click="deactivateProduct" class="btn btn-warning">
                    <i class="icon-pause"></i>
                    Deactivate
                </button>
                <button v-else @click="activateProduct" class="btn btn-success">
                    <i class="icon-play"></i>
                    Activate
                </button>
                <button @click="deleteProduct" class="btn btn-danger">
                    <i class="icon-trash"></i>
                    Delete
                </button>
            </div>
        </div>

        <div v-if="loading" class="loading">Loading product details...</div>

        <div v-else-if="product" class="product-details">
            <!-- Status Banner -->
            <div v-if="!product.is_available" class="status-banner warning">
                <i class="icon-warning"></i>
                This product is not currently available: {{ product.availability_status }}
            </div>

            <div class="details-grid">
                <!-- Main Information -->
                <div class="main-info">
                    <div class="product-image">
                        <img v-if="product.image" :src="product.image" :alt="product.name" class="product-img" />
                        <div v-else class="no-image">
                            <i class="icon-image"></i>
                            <span>No Image</span>
                        </div>
                    </div>

                    <div class="product-info">
                        <div class="info-section">
                            <h2>Basic Information</h2>
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>Name</label>
                                    <span>{{ product.name }}</span>
                                </div>
                                <div class="info-item">
                                    <label>Category</label>
                                    <span>{{ product.category_name || '-' }}</span>
                                </div>
                                <div class="info-item">
                                    <label>SKU</label>
                                    <span>{{ product.sku || '-' }}</span>
                                </div>
                                <div class="info-item">
                                    <label>Status</label>
                                    <span :class="getStatusClass(product.availability_status)" class="status-badge">
                                        {{ product.availability_status }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div v-if="product.short_description" class="info-section">
                            <h3>Short Description</h3>
                            <p>{{ product.short_description }}</p>
                        </div>

                        <div v-if="product.description" class="info-section">
                            <h3>Description</h3>
                            <p class="description">{{ product.description }}</p>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Information -->
                <div class="sidebar-info">
                    <!-- Pricing & Inventory -->
                    <div class="info-card">
                        <h3>Pricing & Inventory</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Price</label>
                                <span class="price">€{{ product.price }}</span>
                            </div>
                            <div class="info-item">
                                <label>Quantity Available</label>
                                <span class="quantity">{{ product.quantity_available }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Physical Properties -->
                    <div v-if="product.weight || product.dimensions" class="info-card">
                        <h3>Physical Properties</h3>
                        <div class="info-grid">
                            <div v-if="product.weight" class="info-item">
                                <label>Weight</label>
                                <span>{{ product.weight }} kg</span>
                            </div>
                            <div v-if="product.dimensions" class="info-item">
                                <label>Dimensions</label>
                                <span>{{ product.dimensions }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Temporal Validity -->
                    <div class="info-card">
                        <h3>Temporal Validity</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Valid From</label>
                                <span>{{ formatDateTime(product.valid_from) || 'Not set' }}</span>
                            </div>
                            <div class="info-item">
                                <label>Valid Until</label>
                                <span>{{ formatDateTime(product.valid_until) || 'Not set' }}</span>
                            </div>
                            <div class="info-item">
                                <label>Currently Valid</label>
                                <span :class="product.is_currently_valid ? 'text-success' : 'text-danger'">
                                    {{ product.is_currently_valid ? 'Yes' : 'No' }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Activation -->
                    <div class="info-card">
                        <h3>Activation</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Active</label>
                                <span :class="product.is_active ? 'text-success' : 'text-danger'">
                                    {{ product.is_active ? 'Yes' : 'No' }}
                                </span>
                            </div>
                            <div v-if="product.activated_at" class="info-item">
                                <label>Activated At</label>
                                <span>{{ formatDateTime(product.activated_at) }}</span>
                            </div>
                            <div v-if="product.deactivated_at" class="info-item">
                                <label>Deactivated At</label>
                                <span>{{ formatDateTime(product.deactivated_at) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Timestamps -->
                    <div class="info-card">
                        <h3>Timestamps</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Created</label>
                                <span>{{ formatDateTime(product.created_at) }}</span>
                            </div>
                            <div class="info-item">
                                <label>Modified</label>
                                <span>{{ formatDateTime(product.modified_at) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-else-if="!loading" class="error-state">
            <h3>Product not found</h3>
            <p>The requested product could not be found.</p>
            <button @click="$router.push('/products')" class="btn btn-primary">Back to Products</button>
        </div>
    </div>
</template>

<script setup>
    import {ref, onMounted} from 'vue';
    import {useRoute, useRouter} from 'vue-router';
    import {productService} from '@/api/product.js';
    import {useToast} from '@horizon/composables/toast';

    const route = useRoute();
    const router = useRouter();
    const {showToast} = useToast();

    // Reactive data
    const product = ref(null);
    const loading = ref(false);

    // Methods
    const loadProduct = async () => {
        loading.value = true;
        try {
            const response = await productService.getById(route.params.id);
            product.value = response;
        } catch (error) {
            showToast('Error loading product', 'error');
            console.error('Error loading product:', error);
        } finally {
            loading.value = false;
        }
    };

    const editProduct = () => {
        router.push(`/products/${route.params.id}/edit`);
    };

    const activateProduct = async () => {
        try {
            await productService.activate(route.params.id);
            showToast('Product activated successfully', 'success');
            loadProduct();
        } catch (error) {
            showToast('Error activating product', 'error');
            console.error('Error activating product:', error);
        }
    };

    const deactivateProduct = async () => {
        try {
            await productService.deactivate(route.params.id);
            showToast('Product deactivated successfully', 'success');
            loadProduct();
        } catch (error) {
            showToast('Error deactivating product', 'error');
            console.error('Error deactivating product:', error);
        }
    };

    const deleteProduct = async () => {
        if (!confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
            return;
        }

        try {
            await productService.delete(route.params.id);
            showToast('Product deleted successfully', 'success');
            router.push('/products');
        } catch (error) {
            showToast('Error deleting product', 'error');
            console.error('Error deleting product:', error);
        }
    };

    const getStatusClass = status => {
        if (status.includes('available')) return 'status-success';
        if (status.includes('Inactive') || status.includes('Not valid')) return 'status-danger';
        if (status.includes('Out of stock')) return 'status-warning';
        return 'status-secondary';
    };

    const formatDateTime = dateString => {
        if (!dateString) return null;
        return new Date(dateString).toLocaleString();
    };

    // Lifecycle
    onMounted(() => {
        loadProduct();
    });
</script>

<style scoped>
    .product-detail-view {
        padding: 1rem;
        max-width: 1400px;
        margin: 0 auto;
    }

    .header-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .header-left {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .header-actions {
        display: flex;
        gap: 1rem;
    }

    .status-banner {
        background: #fef3c7;
        border: 1px solid #f59e0b;
        color: #92400e;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .status-banner.warning {
        background: #fef3c7;
        border-color: #f59e0b;
        color: #92400e;
    }

    .details-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 2rem;
    }

    .main-info {
        background: white;
        border-radius: 8px;
        padding: 2rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .product-image {
        margin-bottom: 2rem;
    }

    .product-img {
        width: 100%;
        max-width: 400px;
        height: 300px;
        object-fit: cover;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
    }

    .no-image {
        width: 100%;
        max-width: 400px;
        height: 300px;
        background: #f9fafb;
        border: 2px dashed #d1d5db;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #6b7280;
        font-size: 1.125rem;
    }

    .no-image i {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .info-section {
        margin-bottom: 2rem;
    }

    .info-section:last-child {
        margin-bottom: 0;
    }

    .info-section h2 {
        color: #111827;
        margin-bottom: 1rem;
        font-size: 1.25rem;
        font-weight: 600;
    }

    .info-section h3 {
        color: #374151;
        margin-bottom: 1rem;
        font-size: 1.125rem;
        font-weight: 600;
    }

    .description {
        line-height: 1.6;
        color: #4b5563;
        white-space: pre-wrap;
    }

    .sidebar-info {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .info-card {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .info-card h3 {
        color: #374151;
        margin-bottom: 1rem;
        font-size: 1rem;
        font-weight: 600;
        border-bottom: 1px solid #e5e7eb;
        padding-bottom: 0.5rem;
    }

    .info-grid {
        display: grid;
        gap: 1rem;
    }

    .info-item {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .info-item label {
        font-size: 0.875rem;
        font-weight: 500;
        color: #6b7280;
    }

    .info-item span {
        color: #111827;
    }

    .price {
        font-size: 1.25rem;
        font-weight: 600;
        color: #059669;
    }

    .quantity {
        font-weight: 600;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
        display: inline-block;
        width: fit-content;
    }

    .status-success {
        background: #d1fae5;
        color: #065f46;
    }

    .status-danger {
        background: #fee2e2;
        color: #991b1b;
    }

    .status-warning {
        background: #fef3c7;
        color: #92400e;
    }

    .status-secondary {
        background: #f3f4f6;
        color: #374151;
    }

    .text-success {
        color: #059669;
        font-weight: 500;
    }

    .text-danger {
        color: #dc2626;
        font-weight: 500;
    }

    .loading {
        text-align: center;
        padding: 4rem 2rem;
        color: #6b7280;
    }

    .error-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #6b7280;
    }

    .error-state h3 {
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .error-state p {
        margin-bottom: 2rem;
    }

    @media (max-width: 1024px) {
        .details-grid {
            grid-template-columns: 1fr;
        }

        .header-section {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .header-actions {
            flex-wrap: wrap;
        }
    }

    @media (max-width: 640px) {
        .header-left {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .header-actions {
            width: 100%;
            justify-content: flex-start;
        }
    }
</style>
