<template>
    <div class="service-detail-view">
        <div class="header-section">
            <div class="header-left">
                <button @click="$router.back()" class="btn btn-secondary">
                    <i class="icon-arrow-left"></i>
                    Back
                </button>
                <h1>{{ service?.name || 'Service Details' }}</h1>
            </div>
            <div class="header-actions">
                <button 
                    @click="editService"
                    class="btn btn-primary"
                >
                    <i class="icon-edit"></i>
                    Edit
                </button>
                <button 
                    v-if="service?.is_active"
                    @click="deactivateService"
                    class="btn btn-warning"
                >
                    <i class="icon-pause"></i>
                    Deactivate
                </button>
                <button 
                    v-else
                    @click="activateService"
                    class="btn btn-success"
                >
                    <i class="icon-play"></i>
                    Activate
                </button>
                <button 
                    @click="deleteService"
                    class="btn btn-danger"
                >
                    <i class="icon-trash"></i>
                    Delete
                </button>
            </div>
        </div>

        <div v-if="loading" class="loading">
            Loading service details...
        </div>

        <div v-else-if="service" class="service-details">
            <!-- Status Banner -->
            <div v-if="!service.is_available" class="status-banner warning">
                <i class="icon-warning"></i>
                This service is not currently available: {{ service.availability_status }}
            </div>

            <div class="details-grid">
                <!-- Main Information -->
                <div class="main-info">
                    <div class="service-image">
                        <img 
                            v-if="service.image" 
                            :src="service.image" 
                            :alt="service.name"
                            class="service-img"
                        />
                        <div v-else class="no-image">
                            <i class="icon-image"></i>
                            <span>No Image</span>
                        </div>
                    </div>

                    <div class="service-info">
                        <div class="info-section">
                            <h2>Basic Information</h2>
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>Name</label>
                                    <span>{{ service.name }}</span>
                                </div>
                                <div class="info-item">
                                    <label>Category</label>
                                    <span>{{ service.category_name || '-' }}</span>
                                </div>
                                <div class="info-item">
                                    <label>Status</label>
                                    <span 
                                        :class="getStatusClass(service.availability_status)"
                                        class="status-badge"
                                    >
                                        {{ service.availability_status }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div v-if="service.short_description" class="info-section">
                            <h3>Short Description</h3>
                            <p>{{ service.short_description }}</p>
                        </div>

                        <div v-if="service.description" class="info-section">
                            <h3>Description</h3>
                            <p class="description">{{ service.description }}</p>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Information -->
                <div class="sidebar-info">
                    <!-- Service Details -->
                    <div class="info-card">
                        <h3>Service Details</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Price</label>
                                <span class="price">€{{ service.price }}</span>
                            </div>
                            <div v-if="service.duration_hours" class="info-item">
                                <label>Duration</label>
                                <span>{{ service.duration_hours }} hours</span>
                            </div>
                            <div v-if="service.max_participants" class="info-item">
                                <label>Max Participants</label>
                                <span>{{ service.max_participants }}</span>
                            </div>
                            <div class="info-item">
                                <label>Requires Equipment</label>
                                <span :class="service.requires_equipment ? 'text-success' : 'text-muted'">
                                    {{ service.requires_equipment ? 'Yes' : 'No' }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Temporal Validity -->
                    <div class="info-card">
                        <h3>Temporal Validity</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Valid From</label>
                                <span>{{ formatDateTime(service.valid_from) || 'Not set' }}</span>
                            </div>
                            <div class="info-item">
                                <label>Valid Until</label>
                                <span>{{ formatDateTime(service.valid_until) || 'Not set' }}</span>
                            </div>
                            <div class="info-item">
                                <label>Currently Valid</label>
                                <span :class="service.is_currently_valid ? 'text-success' : 'text-danger'">
                                    {{ service.is_currently_valid ? 'Yes' : 'No' }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Activation -->
                    <div class="info-card">
                        <h3>Activation</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Active</label>
                                <span :class="service.is_active ? 'text-success' : 'text-danger'">
                                    {{ service.is_active ? 'Yes' : 'No' }}
                                </span>
                            </div>
                            <div v-if="service.activated_at" class="info-item">
                                <label>Activated At</label>
                                <span>{{ formatDateTime(service.activated_at) }}</span>
                            </div>
                            <div v-if="service.deactivated_at" class="info-item">
                                <label>Deactivated At</label>
                                <span>{{ formatDateTime(service.deactivated_at) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Timestamps -->
                    <div class="info-card">
                        <h3>Timestamps</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Created</label>
                                <span>{{ formatDateTime(service.created_at) }}</span>
                            </div>
                            <div class="info-item">
                                <label>Modified</label>
                                <span>{{ formatDateTime(service.modified_at) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-else-if="!loading" class="error-state">
            <h3>Service not found</h3>
            <p>The requested service could not be found.</p>
            <button @click="$router.push('/services')" class="btn btn-primary">
                Back to Services
            </button>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { serviceService } from '@/api/service.js';
import { useToast } from '@horizon/composables/toast';

const route = useRoute();
const router = useRouter();
const { showToast } = useToast();

// Reactive data
const service = ref(null);
const loading = ref(false);

// Methods
const loadService = async () => {
    loading.value = true;
    try {
        const response = await serviceService.getById(route.params.id);
        service.value = response;
    } catch (error) {
        showToast('Error loading service', 'error');
        console.error('Error loading service:', error);
    } finally {
        loading.value = false;
    }
};

const editService = () => {
    router.push(`/services/${route.params.id}/edit`);
};

const activateService = async () => {
    try {
        await serviceService.activate(route.params.id);
        showToast('Service activated successfully', 'success');
        loadService();
    } catch (error) {
        showToast('Error activating service', 'error');
        console.error('Error activating service:', error);
    }
};

const deactivateService = async () => {
    try {
        await serviceService.deactivate(route.params.id);
        showToast('Service deactivated successfully', 'success');
        loadService();
    } catch (error) {
        showToast('Error deactivating service', 'error');
        console.error('Error deactivating service:', error);
    }
};

const deleteService = async () => {
    if (!confirm('Are you sure you want to delete this service? This action cannot be undone.')) {
        return;
    }
    
    try {
        await serviceService.delete(route.params.id);
        showToast('Service deleted successfully', 'success');
        router.push('/services');
    } catch (error) {
        showToast('Error deleting service', 'error');
        console.error('Error deleting service:', error);
    }
};

const getStatusClass = (status) => {
    if (status === 'Available') return 'status-success';
    if (status.includes('Inactive') || status.includes('Not valid')) return 'status-danger';
    return 'status-secondary';
};

const formatDateTime = (dateString) => {
    if (!dateString) return null;
    return new Date(dateString).toLocaleString();
};

// Lifecycle
onMounted(() => {
    loadService();
});
</script>

<style scoped>
/* Service detail specific styles - reusing ProductDetailView patterns */
.service-detail-view {
    padding: 1rem;
    max-width: 1400px;
    margin: 0 auto;
}

.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.status-banner {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    color: #92400e;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.details-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.main-info {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.service-image {
    margin-bottom: 2rem;
}

.service-img {
    width: 100%;
    max-width: 400px;
    height: 300px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.no-image {
    width: 100%;
    max-width: 400px;
    height: 300px;
    background: #f9fafb;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    font-size: 1.125rem;
}

.no-image i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.info-section {
    margin-bottom: 2rem;
}

.info-section:last-child {
    margin-bottom: 0;
}

.info-section h2 {
    color: #111827;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.info-section h3 {
    color: #374151;
    margin-bottom: 1rem;
    font-size: 1.125rem;
    font-weight: 600;
}

.description {
    line-height: 1.6;
    color: #4b5563;
    white-space: pre-wrap;
}

.sidebar-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.info-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-card h3 {
    color: #374151;
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.5rem;
}

.info-grid {
    display: grid;
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-item label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
}

.info-item span {
    color: #111827;
}

.price {
    font-size: 1.25rem;
    font-weight: 600;
    color: #059669;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-block;
    width: fit-content;
}

.status-success {
    background: #d1fae5;
    color: #065f46;
}

.status-danger {
    background: #fee2e2;
    color: #991b1b;
}

.status-secondary {
    background: #f3f4f6;
    color: #374151;
}

.text-success {
    color: #059669;
    font-weight: 500;
}

.text-danger {
    color: #dc2626;
    font-weight: 500;
}

.text-muted {
    color: #6b7280;
}

.loading {
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;
}

.error-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;
}

.error-state h3 {
    color: #374151;
    margin-bottom: 0.5rem;
}

.error-state p {
    margin-bottom: 2rem;
}

@media (max-width: 1024px) {
    .details-grid {
        grid-template-columns: 1fr;
    }
    
    .header-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .header-actions {
        flex-wrap: wrap;
    }
}

@media (max-width: 640px) {
    .header-left {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .header-actions {
        width: 100%;
        justify-content: flex-start;
    }
}
</style>
