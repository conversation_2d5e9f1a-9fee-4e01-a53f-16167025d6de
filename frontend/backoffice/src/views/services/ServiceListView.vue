<template>
    <div class="service-list-view">
        <div class="header-section">
            <h1>Services</h1>
            <div class="header-actions">
                <button @click="$router.push('/services/create')" class="btn btn-primary">
                    <i class="icon-plus"></i>
                    Add Service
                </button>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-section">
            <div class="filter-row">
                <div class="filter-group">
                    <label>Search</label>
                    <input
                        v-model="filters.search"
                        type="text"
                        placeholder="Search services..."
                        @input="debouncedSearch"
                        class="form-input" />
                </div>

                <div class="filter-group">
                    <label>Category</label>
                    <select v-model="filters.category" @change="loadServices" class="form-select">
                        <option value="">All Categories</option>
                        <option v-for="category in categories" :key="category.id" :value="category.id">
                            {{ category.name }}
                        </option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>Status</label>
                    <select v-model="filters.available" @change="loadServices" class="form-select">
                        <option value="">All</option>
                        <option value="true">Active</option>
                        <option value="false">Inactive</option>
                    </select>
                </div>

                <div class="filter-group">
                    <button @click="clearFilters" class="btn btn-secondary">Clear Filters</button>
                </div>
            </div>
        </div>

        <!-- Bulk Actions -->
        <div v-if="selectedServices.length > 0" class="bulk-actions">
            <span>{{ selectedServices.length }} services selected</span>
            <button @click="bulkActivate" class="btn btn-success">Activate Selected</button>
            <button @click="bulkDeactivate" class="btn btn-warning">Deactivate Selected</button>
        </div>

        <!-- Services Table -->
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" @change="toggleSelectAll" :checked="allSelected" />
                        </th>
                        <th>Image</th>
                        <th @click="sort('name')" class="sortable">
                            Name
                            <i :class="getSortIcon('name')"></i>
                        </th>
                        <th>Category</th>
                        <th @click="sort('price')" class="sortable">
                            Price
                            <i :class="getSortIcon('price')"></i>
                        </th>
                        <th>Duration</th>
                        <th>Max Participants</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="service in services" :key="service.id">
                        <td>
                            <input type="checkbox" :value="service.id" v-model="selectedServices" />
                        </td>
                        <td>
                            <img
                                v-if="service.image"
                                :src="service.image"
                                :alt="service.name"
                                class="service-thumbnail" />
                            <div v-else class="no-image">No Image</div>
                        </td>
                        <td>
                            <div class="service-name">{{ service.name }}</div>
                            <div class="service-description">{{ service.short_description }}</div>
                        </td>
                        <td>{{ service.category_name || '-' }}</td>
                        <td class="price">€{{ service.price }}</td>
                        <td>{{ service.duration_hours ? service.duration_hours + 'h' : '-' }}</td>
                        <td>{{ service.max_participants || '-' }}</td>
                        <td>
                            <span :class="getStatusClass(service.availability_status)" class="status-badge">
                                {{ service.availability_status }}
                            </span>
                        </td>
                        <td class="actions">
                            <button @click="viewService(service.id)" class="btn btn-sm btn-info" title="View">
                                <i class="icon-eye"></i>
                            </button>
                            <button @click="editService(service.id)" class="btn btn-sm btn-primary" title="Edit">
                                <i class="icon-edit"></i>
                            </button>
                            <button
                                v-if="service.is_active"
                                @click="deactivateService(service.id)"
                                class="btn btn-sm btn-warning"
                                title="Deactivate">
                                <i class="icon-pause"></i>
                            </button>
                            <button
                                v-else
                                @click="activateService(service.id)"
                                class="btn btn-sm btn-success"
                                title="Activate">
                                <i class="icon-play"></i>
                            </button>
                            <button @click="deleteService(service.id)" class="btn btn-sm btn-danger" title="Delete">
                                <i class="icon-trash"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div v-if="pagination.pages > 1" class="pagination">
            <button
                @click="changePage(pagination.current - 1)"
                :disabled="pagination.current <= 1"
                class="btn btn-secondary">
                Previous
            </button>

            <span class="page-info">
                Page {{ pagination.current }} of {{ pagination.pages }} ({{ pagination.count }} total)
            </span>

            <button
                @click="changePage(pagination.current + 1)"
                :disabled="pagination.current >= pagination.pages"
                class="btn btn-secondary">
                Next
            </button>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="loading">Loading services...</div>

        <!-- Empty State -->
        <div v-if="!loading && services.length === 0" class="empty-state">
            <h3>No services found</h3>
            <p>Create your first service to get started.</p>
            <button @click="$router.push('/services/create')" class="btn btn-primary">Add Service</button>
        </div>
    </div>
</template>

<script setup>
    import {ref, reactive, computed, onMounted} from 'vue';
    import {useRouter} from 'vue-router';
    import {serviceService} from '@/api/service.js';
    import {categoryService} from '@/api/category.js';
    import {useToast} from '@horizon/composables/toast';
    import {debounce} from 'lodash-es';

    const router = useRouter();
    const {showToast} = useToast();

    // Reactive data
    const services = ref([]);
    const categories = ref([]);
    const selectedServices = ref([]);
    const loading = ref(false);

    const filters = reactive({
        search: '',
        category: '',
        available: '',
    });

    const sorting = reactive({
        field: 'name',
        direction: 'asc',
    });

    const pagination = reactive({
        current: 1,
        pages: 1,
        count: 0,
        pageSize: 25,
    });

    // Computed properties
    const allSelected = computed(() => {
        return services.value.length > 0 && selectedServices.value.length === services.value.length;
    });

    // Methods
    const loadServices = async (page = 1) => {
        loading.value = true;
        try {
            const params = {
                page,
                page_size: pagination.pageSize,
                ordering: sorting.direction === 'desc' ? `-${sorting.field}` : sorting.field,
                ...filters,
            };

            // Remove empty filters
            Object.keys(params).forEach(key => {
                if (params[key] === '' || params[key] === null || params[key] === undefined) {
                    delete params[key];
                }
            });

            const response = await serviceService.getAll(params);

            services.value = response.results || [];
            pagination.current = page;
            pagination.pages = response.pages || 1;
            pagination.count = response.count || 0;
        } catch (error) {
            showToast('Error loading services', 'error');
            console.error('Error loading services:', error);
        } finally {
            loading.value = false;
        }
    };

    const loadCategories = async () => {
        try {
            const response = await categoryService.getOptions();
            categories.value = response;
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    };

    const debouncedSearch = debounce(() => {
        loadServices(1);
    }, 300);

    const clearFilters = () => {
        filters.search = '';
        filters.category = '';
        filters.available = '';
        loadServices(1);
    };

    const sort = field => {
        if (sorting.field === field) {
            sorting.direction = sorting.direction === 'asc' ? 'desc' : 'asc';
        } else {
            sorting.field = field;
            sorting.direction = 'asc';
        }
        loadServices(1);
    };

    const getSortIcon = field => {
        if (sorting.field !== field) return 'icon-sort';
        return sorting.direction === 'asc' ? 'icon-sort-up' : 'icon-sort-down';
    };

    const getStatusClass = status => {
        if (status === 'Available') return 'status-success';
        if (status.includes('Inactive') || status.includes('Not valid')) return 'status-danger';
        return 'status-secondary';
    };

    const toggleSelectAll = () => {
        if (allSelected.value) {
            selectedServices.value = [];
        } else {
            selectedServices.value = services.value.map(s => s.id);
        }
    };

    const changePage = page => {
        if (page >= 1 && page <= pagination.pages) {
            loadServices(page);
        }
    };

    const viewService = id => {
        router.push(`/services/${id}`);
    };

    const editService = id => {
        router.push(`/services/${id}/edit`);
    };

    const activateService = async id => {
        try {
            await serviceService.activate(id);
            showToast('Service activated successfully', 'success');
            loadServices(pagination.current);
        } catch (error) {
            showToast('Error activating service', 'error');
            console.error('Error activating service:', error);
        }
    };

    const deactivateService = async id => {
        try {
            await serviceService.deactivate(id);
            showToast('Service deactivated successfully', 'success');
            loadServices(pagination.current);
        } catch (error) {
            showToast('Error deactivating service', 'error');
            console.error('Error deactivating service:', error);
        }
    };

    const deleteService = async id => {
        if (!confirm('Are you sure you want to delete this service?')) return;

        try {
            await serviceService.delete(id);
            showToast('Service deleted successfully', 'success');
            loadServices(pagination.current);
        } catch (error) {
            showToast('Error deleting service', 'error');
            console.error('Error deleting service:', error);
        }
    };

    const bulkActivate = async () => {
        try {
            await serviceService.bulkActivate(selectedServices.value);
            showToast(`${selectedServices.value.length} services activated`, 'success');
            selectedServices.value = [];
            loadServices(pagination.current);
        } catch (error) {
            showToast('Error activating services', 'error');
            console.error('Error activating services:', error);
        }
    };

    const bulkDeactivate = async () => {
        try {
            await serviceService.bulkDeactivate(selectedServices.value);
            showToast(`${selectedServices.value.length} services deactivated`, 'success');
            selectedServices.value = [];
            loadServices(pagination.current);
        } catch (error) {
            showToast('Error deactivating services', 'error');
            console.error('Error deactivating services:', error);
        }
    };

    // Lifecycle
    onMounted(() => {
        loadServices();
        loadCategories();
    });
</script>

<style scoped>
    /* Service list specific styles - reusing ProductListView patterns */
    .service-list-view {
        padding: 1rem;
    }

    .header-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .header-actions {
        display: flex;
        gap: 1rem;
    }

    .filters-section {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .filter-row {
        display: flex;
        gap: 1rem;
        align-items: end;
        flex-wrap: wrap;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        min-width: 200px;
    }

    .filter-group label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #374151;
    }

    .bulk-actions {
        background: #f3f4f6;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .table-container {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
    }

    .data-table th,
    .data-table td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
    }

    .data-table th {
        background: #f9fafb;
        font-weight: 600;
        color: #374151;
    }

    .sortable {
        cursor: pointer;
        user-select: none;
    }

    .sortable:hover {
        background: #f3f4f6;
    }

    .service-thumbnail {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 4px;
    }

    .no-image {
        width: 50px;
        height: 50px;
        background: #f3f4f6;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        color: #6b7280;
        border-radius: 4px;
    }

    .service-name {
        font-weight: 500;
        color: #111827;
    }

    .service-description {
        font-size: 0.875rem;
        color: #6b7280;
        margin-top: 0.25rem;
    }

    .price {
        font-weight: 600;
        color: #059669;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .status-success {
        background: #d1fae5;
        color: #065f46;
    }

    .status-danger {
        background: #fee2e2;
        color: #991b1b;
    }

    .status-secondary {
        background: #f3f4f6;
        color: #374151;
    }

    .actions {
        display: flex;
        gap: 0.5rem;
    }

    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin-top: 2rem;
    }

    .page-info {
        color: #6b7280;
    }

    .loading {
        text-align: center;
        padding: 2rem;
        color: #6b7280;
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #6b7280;
    }

    .empty-state h3 {
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        margin-bottom: 2rem;
    }
</style>
