<template>
    <div class="service-form-view">
        <div class="header-section">
            <h1>{{ isEdit ? 'Edit Service' : 'Create Service' }}</h1>
            <div class="header-actions">
                <button @click="$router.back()" class="btn btn-secondary">
                    <i class="icon-arrow-left"></i>
                    Back
                </button>
            </div>
        </div>

        <form @submit.prevent="saveService" class="service-form">
            <div class="form-grid">
                <!-- Basic Information -->
                <div class="form-section">
                    <h3>Basic Information</h3>
                    
                    <div class="form-group">
                        <label for="name" class="required">Name</label>
                        <input
                            id="name"
                            v-model="form.name"
                            type="text"
                            class="form-input"
                            :class="{ 'error': errors.name }"
                            required
                        />
                        <span v-if="errors.name" class="error-message">{{ errors.name[0] }}</span>
                    </div>

                    <div class="form-group">
                        <label for="short_description">Short Description</label>
                        <textarea
                            id="short_description"
                            v-model="form.short_description"
                            class="form-textarea"
                            :class="{ 'error': errors.short_description }"
                            rows="3"
                            maxlength="500"
                        ></textarea>
                        <span v-if="errors.short_description" class="error-message">{{ errors.short_description[0] }}</span>
                        <span class="char-count">{{ form.short_description?.length || 0 }}/500</span>
                    </div>

                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea
                            id="description"
                            v-model="form.description"
                            class="form-textarea"
                            :class="{ 'error': errors.description }"
                            rows="6"
                        ></textarea>
                        <span v-if="errors.description" class="error-message">{{ errors.description[0] }}</span>
                    </div>

                    <div class="form-group">
                        <label for="category">Category</label>
                        <select
                            id="category"
                            v-model="form.category"
                            class="form-select"
                            :class="{ 'error': errors.category }"
                        >
                            <option value="">Select a category</option>
                            <option 
                                v-for="category in categories" 
                                :key="category.id" 
                                :value="category.id"
                            >
                                {{ category.name }}
                            </option>
                        </select>
                        <span v-if="errors.category" class="error-message">{{ errors.category[0] }}</span>
                    </div>
                </div>

                <!-- Service Details -->
                <div class="form-section">
                    <h3>Service Details</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="price" class="required">Price (€)</label>
                            <input
                                id="price"
                                v-model.number="form.price"
                                type="number"
                                step="0.01"
                                min="0"
                                class="form-input"
                                :class="{ 'error': errors.price }"
                                required
                            />
                            <span v-if="errors.price" class="error-message">{{ errors.price[0] }}</span>
                        </div>

                        <div class="form-group">
                            <label for="duration_hours">Duration (hours)</label>
                            <input
                                id="duration_hours"
                                v-model.number="form.duration_hours"
                                type="number"
                                min="1"
                                class="form-input"
                                :class="{ 'error': errors.duration_hours }"
                            />
                            <span v-if="errors.duration_hours" class="error-message">{{ errors.duration_hours[0] }}</span>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="max_participants">Max Participants</label>
                            <input
                                id="max_participants"
                                v-model.number="form.max_participants"
                                type="number"
                                min="1"
                                class="form-input"
                                :class="{ 'error': errors.max_participants }"
                            />
                            <span v-if="errors.max_participants" class="error-message">{{ errors.max_participants[0] }}</span>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input
                                    v-model="form.requires_equipment"
                                    type="checkbox"
                                    class="form-checkbox"
                                />
                                <span class="checkmark"></span>
                                Requires Equipment
                            </label>
                            <small class="help-text">
                                Check if this service requires special equipment
                            </small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="image">Image</label>
                        <input
                            id="image"
                            type="file"
                            accept="image/*"
                            @change="handleImageUpload"
                            class="form-input"
                        />
                        <div v-if="form.image && typeof form.image === 'string'" class="current-image">
                            <img :src="form.image" alt="Current service image" />
                            <span>Current image</span>
                        </div>
                        <span v-if="errors.image" class="error-message">{{ errors.image[0] }}</span>
                    </div>
                </div>

                <!-- Temporal Validity -->
                <div class="form-section">
                    <h3>Temporal Validity</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="valid_from">Valid From</label>
                            <input
                                id="valid_from"
                                v-model="form.valid_from"
                                type="datetime-local"
                                class="form-input"
                                :class="{ 'error': errors.valid_from }"
                            />
                            <span v-if="errors.valid_from" class="error-message">{{ errors.valid_from[0] }}</span>
                        </div>

                        <div class="form-group">
                            <label for="valid_until">Valid Until</label>
                            <input
                                id="valid_until"
                                v-model="form.valid_until"
                                type="datetime-local"
                                class="form-input"
                                :class="{ 'error': errors.valid_until }"
                            />
                            <span v-if="errors.valid_until" class="error-message">{{ errors.valid_until[0] }}</span>
                        </div>
                    </div>
                </div>

                <!-- Activation -->
                <div class="form-section">
                    <h3>Activation</h3>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input
                                v-model="form.is_active"
                                type="checkbox"
                                class="form-checkbox"
                            />
                            <span class="checkmark"></span>
                            Active
                        </label>
                        <small class="help-text">
                            Inactive services will not be available for booking
                        </small>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <button type="button" @click="$router.back()" class="btn btn-secondary">
                    Cancel
                </button>
                <button type="submit" :disabled="loading" class="btn btn-primary">
                    <span v-if="loading">Saving...</span>
                    <span v-else>{{ isEdit ? 'Update Service' : 'Create Service' }}</span>
                </button>
            </div>
        </form>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { serviceService } from '@/api/service.js';
import { categoryService } from '@/api/category.js';
import { useToast } from '@horizon/composables/toast';

const route = useRoute();
const router = useRouter();
const { showToast } = useToast();

// Reactive data
const loading = ref(false);
const categories = ref([]);
const errors = ref({});

const form = reactive({
    name: '',
    description: '',
    short_description: '',
    price: null,
    duration_hours: null,
    category: '',
    image: null,
    max_participants: null,
    requires_equipment: false,
    valid_from: '',
    valid_until: '',
    is_active: true,
});

// Computed properties
const isEdit = computed(() => !!route.params.id);
const serviceId = computed(() => route.params.id);

// Methods
const loadCategories = async () => {
    try {
        const response = await categoryService.getOptions();
        categories.value = response;
    } catch (error) {
        console.error('Error loading categories:', error);
    }
};

const loadService = async () => {
    if (!isEdit.value) return;
    
    loading.value = true;
    try {
        const service = await serviceService.getById(serviceId.value);
        
        // Populate form with service data
        Object.keys(form).forEach(key => {
            if (service[key] !== undefined) {
                form[key] = service[key];
            }
        });

        // Format datetime fields for input
        if (service.valid_from) {
            form.valid_from = new Date(service.valid_from).toISOString().slice(0, 16);
        }
        if (service.valid_until) {
            form.valid_until = new Date(service.valid_until).toISOString().slice(0, 16);
        }
    } catch (error) {
        showToast('Error loading service', 'error');
        console.error('Error loading service:', error);
        router.push('/services');
    } finally {
        loading.value = false;
    }
};

const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
        form.image = file;
    }
};

const saveService = async () => {
    loading.value = true;
    errors.value = {};

    try {
        // Prepare form data
        const formData = new FormData();
        
        Object.keys(form).forEach(key => {
            if (form[key] !== null && form[key] !== '') {
                if (key === 'image' && form[key] instanceof File) {
                    formData.append(key, form[key]);
                } else if (key !== 'image' || typeof form[key] !== 'string') {
                    formData.append(key, form[key]);
                }
            }
        });

        // Convert datetime fields back to ISO format
        if (form.valid_from) {
            formData.set('valid_from', new Date(form.valid_from).toISOString());
        }
        if (form.valid_until) {
            formData.set('valid_until', new Date(form.valid_until).toISOString());
        }

        let response;
        if (isEdit.value) {
            response = await serviceService.update(serviceId.value, formData);
        } else {
            response = await serviceService.create(formData);
        }

        showToast(
            `Service ${isEdit.value ? 'updated' : 'created'} successfully`,
            'success'
        );
        
        router.push(`/services/${response.id}`);
    } catch (error) {
        if (error.response?.data) {
            errors.value = error.response.data;
        }
        showToast(
            `Error ${isEdit.value ? 'updating' : 'creating'} service`,
            'error'
        );
        console.error('Error saving service:', error);
    } finally {
        loading.value = false;
    }
};

// Lifecycle
onMounted(() => {
    loadCategories();
    loadService();
});
</script>

<style scoped>
/* Service form specific styles - reusing ProductFormView patterns */
.service-form-view {
    padding: 1rem;
    max-width: 1200px;
    margin: 0 auto;
}

.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.service-form {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-grid {
    display: grid;
    gap: 2rem;
}

.form-section {
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 2rem;
}

.form-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.form-section h3 {
    color: #374151;
    margin-bottom: 1.5rem;
    font-size: 1.125rem;
    font-weight: 600;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #374151;
}

.form-group label.required::after {
    content: ' *';
    color: #ef4444;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error,
.form-textarea.error,
.form-select.error {
    border-color: #ef4444;
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal !important;
}

.form-checkbox {
    margin-right: 0.5rem;
    width: auto;
}

.error-message {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
}

.char-count {
    color: #6b7280;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: block;
}

.help-text {
    color: #6b7280;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
}

.current-image {
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.current-image img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 6px;
    border: 1px solid #d1d5db;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .header-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>
